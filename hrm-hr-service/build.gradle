plugins {
	id 'java'
	id 'org.springframework.boot' version '3.5.3'
	id 'io.spring.dependency-management' version '1.1.7'
}

group = 'com.gianghp'
version = '0.0.1-SNAPSHOT'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(21)
	}
}

configurations {
	compileOnly {
		extendsFrom annotationProcessor
	}
}

repositories {
	mavenCentral()
}

dependencies {
	// HRM Common - chứa JWT validation logic
	implementation 'com.gianghp:hrm-common'
	implementation 'org.mapstruct:mapstruct:1.6.3'

	// Spring Boot starters
	implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'
	implementation 'org.springdoc:springdoc-openapi-starter-webmvc-api:2.5.0'
	implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.5.0'
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'org.springframework.boot:spring-boot-starter-validation'
	implementation 'org.apache.kafka:kafka-clients:4.0.0'
	implementation 'org.springframework.kafka:spring-kafka'

	// Database
	implementation 'org.postgresql:postgresql'

	// Lombok
	compileOnly 'org.projectlombok:lombok'
	annotationProcessor 'org.projectlombok:lombok'

	// MapStruct - must be after Lombok
	annotationProcessor 'org.mapstruct:mapstruct-processor:1.6.3'

	// Development
	developmentOnly 'org.springframework.boot:spring-boot-devtools'
	annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'

	// Testing
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

tasks.named('test') {
	useJUnitPlatform()
}

bootRun {
	mainClass = 'com.gianghp.hrm_hr_service.HrmHrServiceApplication'
}
