version: '3.8'
services:
  hr-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: hr-service
    networks:
      - internal
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.hr.rule=PathPrefix(`/hr-service`)"
      - "traefik.http.middlewares.hr-stripprefix.stripprefix.prefixes=/hr-service"
      - "traefik.http.routers.hr.entrypoints=web"
      - "traefik.http.routers.hr.middlewares=hr-stripprefix"
      - "traefik.http.services.hr.loadbalancer.server.port=6020"
      - "traefik.http.services.hr.loadbalancer.healthcheck.path=/health"
      - "traefik.http.services.hr.loadbalancer.healthcheck.interval=10s"
      - "traefik.http.services.hr.loadbalancer.healthcheck.timeout=3s"

networks:
  internal:
    external: true

