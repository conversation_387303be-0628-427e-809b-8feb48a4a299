package com.gianghp.hrm_hr_service.consumers;

import com.gianghp.hrm.constants.TopicConstants;
import com.gianghp.hrm.dtos.EmployeeBasicDto;
import com.gianghp.hrm.dtos.EmployeeCreateFailedDto;
import com.gianghp.hrm_hr_service.acl.EmployeeACL;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class KafkaHrConsumer {

    private final EmployeeACL employeeACL;

    @KafkaListener(topics = TopicConstants.USER_WITH_EMPLOYEE_CREATED)
    public void consumeEmployeeCreated(EmployeeBasicDto message) {
        log.info("Received from {}: {}", TopicConstants.USER_WITH_EMPLOYEE_CREATED, message);
        employeeACL.handleEmployeeCreatedEvent(message);
    }

    @KafkaListener(topics = TopicConstants.EMPLOYEE_CREATE_FAILED)
    public void consumeEmployeeCreateFailed(EmployeeCreateFailedDto message) {
        log.info("Received from {}: {}", TopicConstants.EMPLOYEE_CREATE_FAILED, message);
        if (message.getEmployeeId() == null) {
            log.error("Employee id is null, cannot delete user");
            return;
        }
        employeeACL.handleEmployeeCreateFailedEvent(message.getEmployeeId());
    }
}
