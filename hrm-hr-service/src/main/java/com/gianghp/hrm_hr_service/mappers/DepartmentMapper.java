package com.gianghp.hrm_hr_service.mappers;

import com.gianghp.hrm_hr_service.dtos.DepartmentDto;
import com.gianghp.hrm.dtos.EmployeeBasicDto;
import com.gianghp.hrm_hr_service.entities.Department;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public interface DepartmentMapper {

  DepartmentMapper INSTANCE = Mappers.getMapper(DepartmentMapper.class);


  @Mapping(target = "managerId", expression = "java(department.getManager() != null ? department.getManager().getId() : null)")
  @Mapping(target = "managerName", expression = "java(department.getManager() != null ? department.getManager().getFirstName() + \" \" + department.getManager().getLastName() : null)")
  @Mapping(target = "employeeCount", expression = "java(department.getEmployees() != null ? department.getEmployees().size() : 0)")
  @Mapping(target = "sampleEmployees", expression = "java(mapSampleEmployees(department))")
  DepartmentDto toDto(Department department);

  @Mapping(target = "employees", ignore = true)
  @Mapping(target = "manager", ignore = true)
  Department toEntity(DepartmentDto dto);

  default List<EmployeeBasicDto> mapSampleEmployees(Department department) {
      if (department.getEmployees() == null) {
          return List.of();
      }

    return department.getEmployees().stream()
        .limit(3)
        .map(emp -> EmployeeBasicDto.builder()
            .id(emp.getId())
            .email(emp.getEmail())
            .fullName(emp.getFirstName() + " " + emp.getLastName())
            .designationName(emp.getDesignation().getTitle())
            .build())
        .collect(Collectors.toList());
  }
}
