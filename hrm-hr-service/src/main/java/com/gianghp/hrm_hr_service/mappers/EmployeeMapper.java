package com.gianghp.hrm_hr_service.mappers;

import com.gianghp.hrm.dtos.EmployeeBasicDto;
import com.gianghp.hrm_hr_service.dtos.EmployeeDto;
import com.gianghp.hrm_hr_service.entities.Department;
import com.gianghp.hrm_hr_service.entities.Designation;
import com.gianghp.hrm_hr_service.entities.Employee;
import com.gianghp.hrm_hr_service.repositories.EmployeeRepository;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.UUID;

@Mapper(componentModel = "spring")
public interface EmployeeMapper {
    EmployeeMapper INSTANCE = Mappers.getMapper(EmployeeMapper.class);

    @Mapping(target = "type", source = "workType")
    @Mapping(target = "departmentId", expression = "java(employee.getDepartment() != null ? employee.getDepartment().getId() : null)")
    @Mapping(target = "departmentName", expression = "java(employee.getDepartment() != null ? employee.getDepartment().getName() : null)")
    @Mapping(target = "designationId", expression = "java(employee.getDesignation() != null ? employee.getDesignation().getId() : null)")
    @Mapping(target = "designationName", expression = "java(employee.getDesignation() != null ? employee.getDesignation().getTitle() : null)")
    EmployeeDto toDto(Employee employee);

    @Mapping(target = "department", ignore = true)
    @Mapping(target = "designation", ignore = true)
    @Mapping(target = "userId", ignore = true)
    @Mapping(target = "workType", source="type")
    Employee toEntity(EmployeeDto dto);


    @Mapping(target = "department", ignore = true)
    @Mapping(target = "designation", ignore = true)
    @Mapping(target = "workType", source="type")
    @Mapping(target = "dateOfLeaving", ignore = true)
    @Mapping(target = "status", ignore = true)
    Employee toEntity(EmployeeBasicDto dto);

    @Mapping(target = "designationName", expression = "java(employee.getDesignation() != null ? employee.getDesignation().getTitle() : null)")
    @Mapping(target = "departmentName", expression = "java(employee.getDepartment() != null ? employee.getDepartment().getName() : null)")
    @Mapping(target = "fullName", expression = "java(employee.getFirstName() + \" \" + employee.getLastName())")
    @Mapping(target = "departmentId", expression = "java(employee.getDepartment() != null ? employee.getDepartment().getId() : null)")
    EmployeeBasicDto toBasicDto(Employee employee);
}
