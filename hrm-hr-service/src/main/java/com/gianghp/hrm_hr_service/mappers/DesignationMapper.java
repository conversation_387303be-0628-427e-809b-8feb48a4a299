package com.gianghp.hrm_hr_service.mappers;

import com.gianghp.hrm_hr_service.dtos.DesignationDto;
import com.gianghp.hrm_hr_service.entities.Designation;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface DesignationMapper {
    DesignationMapper INSTANCE = Mappers.getMapper(DesignationMapper.class);

    @Mapping(target = "employeeCount", ignore = true)
    DesignationDto toDto(Designation designation);

    Designation toEntity(DesignationDto dto);
}
