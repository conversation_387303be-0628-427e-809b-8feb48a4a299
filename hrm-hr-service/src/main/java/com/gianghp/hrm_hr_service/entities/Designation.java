package com.gianghp.hrm_hr_service.entities;

import com.gianghp.hrm.entities.BaseEntity;
import com.gianghp.hrm_hr_service.entities.Department;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Table(name = "designations")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Designation extends BaseEntity {

    @Column(nullable = false, unique = true, length = 50)
    private String code; // e.g., DSG001

    @Column(nullable = false, length = 100)
    private String title; // e.g., Backend Developer, HR Manager

    @Column(length = 255)
    private String description;
}
