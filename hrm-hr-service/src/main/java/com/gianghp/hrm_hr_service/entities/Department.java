package com.gianghp.hrm_hr_service.entities;

import com.gianghp.hrm.entities.BaseEntity;
import com.gianghp.hrm_hr_service.entities.Employee;
import jakarta.persistence.*;
import lombok.*;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Entity
@Table(name = "departments")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Department extends BaseEntity {

    @Column(nullable = false, unique = true, length = 50)
    private String code; // e.g., DEPT001

    @Column(nullable = false, length = 100)
    private String name; // e.g., Engineering, HR, Finance

    @Column(length = 255)
    private String description;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "manager_id")
    private Employee manager; // optional: người đứng đầu phòng ban

    @OneToMany(mappedBy = "department")
    private List<Employee> employees;

}
