package com.gianghp.hrm_hr_service.producers;

import com.gianghp.hrm.constants.TopicConstants;
import com.gianghp.hrm.dtos.EmployeeBasicDto;
import com.gianghp.hrm.dtos.EmployeeCreateFailedDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
@Slf4j
@RequiredArgsConstructor
public class KafkaHrProducer {

    private final KafkaTemplate<String, EmployeeBasicDto> kafkaTemplate;
    private final KafkaTemplate<String, String> kafkaStringTemplate;
    private final KafkaTemplate<String, UUID> kafkaUUIDTemplate;
    private final KafkaTemplate<String, EmployeeCreateFailedDto> kafkaEmployeeCreateFailedTemplate;

    public void sendEmployeeWhenCreated(EmployeeBasicDto employee) {
        Message<EmployeeBasicDto> msg = MessageBuilder.withPayload(
                employee
        ).setHeader(KafkaHeaders.TOPIC, TopicConstants.EMPLOYEE_CREATED).build();
        log.info("Sent to {}: {}", TopicConstants.EMPLOYEE_CREATED, employee);
        kafkaTemplate.send(msg);
    }

    public void sendEmployeeWhenCreateFailed(EmployeeBasicDto employee) {
        EmployeeCreateFailedDto message = new EmployeeCreateFailedDto();
        if (employee.getUserId() == null) {
            log.error("User id is null, cannot send employee create failed event");
            return;
        }
        message.setUserId(employee.getUserId());
        message.setEmployeeId(employee.getId());
        Message<EmployeeCreateFailedDto> msg = MessageBuilder.withPayload(
                message
        ).setHeader(KafkaHeaders.TOPIC, TopicConstants.EMPLOYEE_CREATE_FAILED).build();
        log.info("Sent to {}: {}", TopicConstants.EMPLOYEE_CREATE_FAILED, message);
        kafkaEmployeeCreateFailedTemplate.send(msg);
    }

    public void sendEmployeeWhenUpdated(EmployeeBasicDto employee) {
        Message<EmployeeBasicDto> msg = MessageBuilder.withPayload(
                employee
        ).setHeader(KafkaHeaders.TOPIC, TopicConstants.EMPLOYEE_UPDATED).build();
        log.info("Sent to {}: {}", TopicConstants.EMPLOYEE_UPDATED, employee);
        kafkaTemplate.send(msg);
    }
}
