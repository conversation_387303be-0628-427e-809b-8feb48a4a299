package com.gianghp.hrm_hr_service.controllers;

import com.gianghp.hrm.dtos.ApiResponse;
import com.gianghp.hrm_hr_service.dtos.DesignationDto;
import com.gianghp.hrm_hr_service.services.DesignationService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/designations")
@RequiredArgsConstructor
public class DesignationController {
    private final DesignationService designationService;

    @GetMapping
    public ResponseEntity<ApiResponse<List<DesignationDto>>> getAllDesignations(
    ) {
        try {
            List<DesignationDto> designations = designationService.getAllDesignations();
            return ResponseEntity.ok(ApiResponse.success("Designations retrieved successfully", designations));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve designations: " + e.getMessage()));
        }
    }

}
