package com.gianghp.hrm_hr_service.dtos;

import com.gianghp.hrm.enums.Gender;
import com.gianghp.hrm.enums.WorkType;
import lombok.*;

import java.time.LocalDate;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EmployeeDto {
    private UUID id;
    private UUID userId;
    private String employeeCode;
    private String firstName;
    private String lastName;
    private String mobileNumber;
    private String email;
    private LocalDate dateOfBirth;
    private Gender gender;
    private String nationality;
    private String address;
    private LocalDate dateOfJoining;
    private LocalDate dateOfLeaving;
    private String state;
    private WorkType type; // office, remote, etc.
    private String status; // active, inactive, etc.
    private UUID departmentId;
    private String departmentName;
    private UUID designationId;
    private String designationName;
}
