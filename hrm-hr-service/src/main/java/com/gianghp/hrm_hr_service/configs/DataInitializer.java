package com.gianghp.hrm_hr_service.configs;

import com.gianghp.hrm.enums.EmployeeStatus;
import com.gianghp.hrm.enums.Gender;
import com.gianghp.hrm.enums.WorkLocationType;
import com.gianghp.hrm.enums.WorkType;
import com.gianghp.hrm_hr_service.entities.Department;
import com.gianghp.hrm_hr_service.entities.Designation;
import com.gianghp.hrm_hr_service.entities.Employee;
import com.gianghp.hrm_hr_service.repositories.DepartmentRepository;
import com.gianghp.hrm_hr_service.repositories.DesignationRepository;
import com.gianghp.hrm_hr_service.repositories.EmployeeRepository;
import com.gianghp.hrm_hr_service.services.EmployeeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Component
@RequiredArgsConstructor
@Slf4j
@Profile("!test")
public class DataInitializer implements CommandLineRunner {

    private final DepartmentRepository departmentRepository;
    private final DesignationRepository designationRepository;

    @Override
    public void run(String... args) {
        log.info("Starting HR Service data initialization...");
        
        initDepartments();
        initDesignations();
        
        log.info("HR Service data initialization completed successfully!");
    }

    private void initDepartments() {
        if (departmentRepository.count() == 0) {
            log.info("Initializing default departments...");

            List<Department> departments = List.of(
                Department.builder()
                    .code("DEPT001")
                    .name("Information Technology")
                    .description("Responsible for software development, system administration, and IT support")
                    .build(),
                    
                Department.builder()
                    .code("DEPT002")
                    .name("Human Resources")
                    .description("Manages employee relations, recruitment, and organizational development")
                    .build(),
                    
                Department.builder()
                    .code("DEPT003")
                    .name("Finance & Accounting")
                    .description("Handles financial planning, accounting, and budget management")
                    .build(),
                    
                Department.builder()
                    .code("DEPT004")
                    .name("Marketing & Sales")
                    .description("Responsible for marketing strategies, sales, and customer relations")
                    .build(),
                    
                Department.builder()
                    .code("DEPT005")
                    .name("Operations")
                    .description("Manages daily operations, logistics, and process optimization")
                    .build(),
                    
                Department.builder()
                    .code("DEPT006")
                    .name("Quality Assurance")
                    .description("Ensures product quality and testing standards")
                    .build()
            );

            departmentRepository.saveAll(departments);
            log.info("Created {} departments", departments.size());
        }
    }

    private void initDesignations() {
        if (designationRepository.count() == 0) {
            log.info("Initializing default designations...");

            List<Designation> designations = List.of(
                // IT Designations
                Designation.builder()
                    .code("DSG001")
                    .title("Software Engineer")
                    .description("Develops and maintains software applications")
                    .build(),
                    
                Designation.builder()
                    .code("DSG002")
                    .title("Senior Software Engineer")
                    .description("Senior level software development with mentoring responsibilities")
                    .build(),
                    
                Designation.builder()
                    .code("DSG003")
                    .title("Tech Lead")
                    .description("Technical leadership and architecture decisions")
                    .build(),
                    
                Designation.builder()
                    .code("DSG004")
                    .title("DevOps Engineer")
                    .description("Infrastructure automation and deployment management")
                    .build(),
                    
                Designation.builder()
                    .code("DSG005")
                    .title("QA Engineer")
                    .description("Quality assurance and testing specialist")
                    .build(),
                    
                // HR Designations
                Designation.builder()
                    .code("DSG006")
                    .title("HR Manager")
                    .description("Manages human resources operations and policies")
                    .build(),
                    
                Designation.builder()
                    .code("DSG007")
                    .title("HR Specialist")
                    .description("Handles recruitment, employee relations, and HR processes")
                    .build(),
                    
                Designation.builder()
                    .code("DSG008")
                    .title("Recruiter")
                    .description("Focuses on talent acquisition and recruitment")
                    .build(),
                    
                // Finance Designations
                Designation.builder()
                    .code("DSG009")
                    .title("Finance Manager")
                    .description("Oversees financial operations and planning")
                    .build(),
                    
                Designation.builder()
                    .code("DSG010")
                    .title("Accountant")
                    .description("Handles accounting and financial record keeping")
                    .build(),
                    
                Designation.builder()
                    .code("DSG011")
                    .title("Financial Analyst")
                    .description("Analyzes financial data and provides insights")
                    .build(),
                    
                // Marketing & Sales Designations
                Designation.builder()
                    .code("DSG012")
                    .title("Marketing Manager")
                    .description("Develops and executes marketing strategies")
                    .build(),
                    
                Designation.builder()
                    .code("DSG013")
                    .title("Sales Representative")
                    .description("Handles customer sales and relationship management")
                    .build(),
                    
                Designation.builder()
                    .code("DSG014")
                    .title("Digital Marketing Specialist")
                    .description("Manages digital marketing campaigns and online presence")
                    .build(),
                    
                // Operations Designations
                Designation.builder()
                    .code("DSG015")
                    .title("Operations Manager")
                    .description("Oversees daily operations and process management")
                    .build(),
                    
                Designation.builder()
                    .code("DSG016")
                    .title("Project Manager")
                    .description("Manages projects from initiation to completion")
                    .build(),
                    
                // Executive Designations
                Designation.builder()
                    .code("DSG017")
                    .title("CEO")
                    .description("Chief Executive Officer - Overall company leadership")
                    .build(),
                    
                Designation.builder()
                    .code("DSG018")
                    .title("CTO")
                    .description("Chief Technology Officer - Technology strategy and leadership")
                    .build(),
                    
                Designation.builder()
                    .code("DSG019")
                    .title("Department Head")
                    .description("Heads a specific department with management responsibilities")
                    .build(),
                    
                Designation.builder()
                    .code("DSG020")
                    .title("Team Lead")
                    .description("Leads a team with coordination and management duties")
                    .build()
            );

            designationRepository.saveAll(designations);
            log.info("Created {} designations", designations.size());
        }
    }
//
//    private void updateDepartmentManagers() {
//        log.info("Setting department managers...");
//
//        // Set CTO as IT department manager
//        Department itDept = departmentRepository.findByCode("DEPT001").orElse(null);
//        Employee cto = employeeRepository.findAll().getFirst();
//
//        if (itDept != null && cto != null) {
//            itDept.setManager(cto);
//            departmentRepository.save(itDept);
//            log.info("Set {} as manager of {}", cto.getFirstName() + " " + cto.getLastName(), itDept.getName());
//        }
//    }
}
