package com.gianghp.hrm_hr_service.repositories;

import com.gianghp.hrm_hr_service.entities.Designation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface DesignationRepository extends JpaRepository<Designation, UUID> {
    Optional<Designation> findByCode(String code);
}
