package com.gianghp.hrm_hr_service.repositories;

import com.gianghp.hrm_hr_service.entities.Employee;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface EmployeeRepository extends JpaRepository<Employee, UUID> {
    Page<Employee> findByDepartmentId(UUID departmentId, Pageable pageable);
    List<Employee> findByDepartmentId(UUID departmentId);
    int countByDesignationId(UUID designationId);
    Optional<Employee> findByEmployeeCode(String employeeCode);

    boolean existsByUserId(UUID userId);

    boolean existsByEmail(String email);

    boolean existsByEmployeeCode(String employeeCode);

    Optional<Employee> findByUserId(UUID userId);

    List<Employee> findByDepartmentIsNull();
}
