package com.gianghp.hrm_hr_service.services;

import com.gianghp.hrm_hr_service.dtos.DesignationDto;
import com.gianghp.hrm_hr_service.entities.Designation;
import com.gianghp.hrm_hr_service.mappers.DesignationMapper;
import com.gianghp.hrm_hr_service.repositories.DesignationRepository;
import com.gianghp.hrm_hr_service.repositories.EmployeeRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional
public class DesignationService {
    private final DesignationRepository designationRepository;
    private final DesignationMapper designationMapper;
    private final EmployeeRepository employeeRepository;

    public List<DesignationDto> getAllDesignations() {
        return designationRepository.findAll().stream()
                .map(designation -> {
                    DesignationDto dto = designationMapper.toDto(designation);
                    int count = employeeRepository.countByDesignationId(designation.getId());
                    dto.setEmployeeCount(count);
                    return dto;
                })
                .toList();
    }

    public DesignationDto getDesignationById(UUID id) {
        Designation designation = designationRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Designation not found with id: " + id));
        return designationMapper.toDto(designation);
    }

    public DesignationDto createDesignation(DesignationDto designationDto) {
        Designation designation = designationMapper.toEntity(designationDto);
        Designation saved = designationRepository.save(designation);
        return designationMapper.toDto(saved);
    }

}
