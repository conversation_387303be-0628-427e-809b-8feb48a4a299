package com.gianghp.hrm_hr_service.services;

import com.gianghp.hrm_hr_service.dtos.DepartmentDto;
import com.gianghp.hrm_hr_service.dtos.EmployeeDto;
import com.gianghp.hrm_hr_service.dtos.UpdateDepartmentDto;
import com.gianghp.hrm_hr_service.entities.Department;
import com.gianghp.hrm_hr_service.entities.Employee;
import com.gianghp.hrm_hr_service.mappers.DepartmentMapper;
import com.gianghp.hrm_hr_service.mappers.EmployeeMapper;
import com.gianghp.hrm_hr_service.repositories.DepartmentRepository;
import com.gianghp.hrm_hr_service.repositories.EmployeeRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional
public class DepartmentService {

    private final DepartmentRepository departmentRepository;
    private final EmployeeRepository employeeRepository;
    private final DepartmentMapper departmentMapper;
    private final EmployeeMapper employeeMapper;

    // ✅ Trả về danh sách DTO
    public List<DepartmentDto> getAllDepartments() {
        List<Department> departments = departmentRepository.findAll();
        return departments.stream()
                .map(departmentMapper::toDto)
                .toList();
    }

    public DepartmentDto getDepartmentById(UUID id) {
        Department department = departmentRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Department not found with id: " + id));
        return departmentMapper.toDto(department);
    }

    public DepartmentDto createDepartment(DepartmentDto departmentDto) {
        Department department = departmentMapper.toEntity(departmentDto);
        Department saved = departmentRepository.save(department);
        return departmentMapper.toDto(saved);
    }

    public Page<EmployeeDto> getEmployeesByDepartment(UUID departmentId, Pageable pageable) {
        Page<Employee> employees = employeeRepository.findByDepartmentId(departmentId, pageable);
        return employees
                .map(employeeMapper::toDto);
    }

    public EmployeeDto getDepartmentManager(UUID departmentId) {
        Department department = departmentRepository.findById(departmentId)
                .orElseThrow(() -> new RuntimeException("Department not found with id: " + departmentId));
        return employeeMapper.toDto(department.getManager());
    }

    public List<EmployeeDto> getAllEmployeesByDepartment(UUID id) {
        return employeeRepository.findByDepartmentId(id).stream()
                .map(employeeMapper::toDto)
                .toList();
    }

    public Page<EmployeeDto> getEmployeesByMyDepartment(UUID userId, Pageable pageable) {
        Employee employee = employeeRepository.findByUserId(userId)
                .orElseThrow(() -> new RuntimeException("Employee not found with user id: " + userId));
        return getEmployeesByDepartment(employee.getDepartment().getId(), pageable);
    }

    public List<EmployeeDto> getAllEmployeesByMyDepartment(UUID userId) {
        Employee employee = employeeRepository.findByUserId(userId)
                .orElseThrow(() -> new RuntimeException("Employee not found with user id: " + userId));
        return getAllEmployeesByDepartment(employee.getDepartment().getId());
    }

    public DepartmentDto getMyDepartment(UUID userId) {
        Employee employee = employeeRepository.findByUserId(userId)
                .orElseThrow(() -> new RuntimeException("Employee not found with user id: " + userId));
        return getDepartmentById(employee.getDepartment().getId());
    }

    public DepartmentDto updateDepartment(UUID id, UpdateDepartmentDto updateDepartmentDto) {
        Department department = departmentRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Department not found with id: " + id));

        // Handle manager update nếu có truyền vào
        if (updateDepartmentDto.getManagerId() != null) {
            Employee manager = employeeRepository.findById(updateDepartmentDto.getManagerId())
                    .orElseThrow(() -> new RuntimeException("Manager not found with id: " + updateDepartmentDto.getManagerId()));

            if (manager.getDepartment() != null && !manager.getDepartment().getId().equals(department.getId())) {
                throw new RuntimeException("Manager is not in the department");
            }

            department.setManager(manager);
        }

        // Cập nhật tên phòng ban nếu có
        if (updateDepartmentDto.getName() != null) {
            department.setName(updateDepartmentDto.getName());
        }

        // Cập nhật mô tả nếu có
        if (updateDepartmentDto.getDescription() != null) {
            department.setDescription(updateDepartmentDto.getDescription());
        }

        // Thêm nhân viên nếu có danh sách
        if (updateDepartmentDto.getAddEmployeeIds() != null && !updateDepartmentDto.getAddEmployeeIds().isEmpty()) {
            List<Employee> addedEmployees = employeeRepository.findAllById(updateDepartmentDto.getAddEmployeeIds());
            for (Employee employee : addedEmployees) {
                employee.setDepartment(department);
                employeeRepository.save(employee);
            }
            department.getEmployees().addAll(addedEmployees);
        }

        // Xoá nhân viên nếu có danh sách
        if (updateDepartmentDto.getRemovedEmployeeIds() != null && !updateDepartmentDto.getRemovedEmployeeIds().isEmpty()) {
            List<Employee> removedEmployees = employeeRepository.findAllById(updateDepartmentDto.getRemovedEmployeeIds());
            for (Employee employee : removedEmployees) {
                employee.setDepartment(null);
                employeeRepository.save(employee);
            }
            department.getEmployees().removeAll(removedEmployees);
        }

        departmentRepository.save(department);
        return departmentMapper.toDto(department);
    }

}
