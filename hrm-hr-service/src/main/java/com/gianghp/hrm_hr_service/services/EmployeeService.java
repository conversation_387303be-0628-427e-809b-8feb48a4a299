package com.gianghp.hrm_hr_service.services;

import com.gianghp.hrm_hr_service.dtos.EmployeeDto;
import com.gianghp.hrm_hr_service.entities.Department;
import com.gianghp.hrm_hr_service.entities.Designation;
import com.gianghp.hrm_hr_service.entities.Employee;
import com.gianghp.hrm_hr_service.mappers.EmployeeMapper;
import com.gianghp.hrm_hr_service.repositories.DepartmentRepository;
import com.gianghp.hrm_hr_service.repositories.DesignationRepository;
import com.gianghp.hrm_hr_service.repositories.EmployeeRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page; // <-- Import Page
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

// import java.util.List; // Không cần List nữa cho getAllEmployees
import java.util.List;
import java.util.UUID;
// import java.util.stream.Collectors; // Không cần Collectors.toList() nữa

@Service
@RequiredArgsConstructor
@Transactional
public class EmployeeService {
    private final EmployeeRepository employeeRepository;
    private final EmployeeMapper employeeMapper;
    private final DepartmentRepository departmentRepository;
    private final DesignationRepository designationRepository;

    // Thay đổi kiểu trả về từ List<EmployeeDto> thành Page<EmployeeDto>
    public Page<EmployeeDto> getAllEmployees(Pageable pageable) {

        Page<Employee> employeePage = employeeRepository.findAll(pageable);

        return employeePage.map(employeeMapper::toDto);
    }

    public EmployeeDto getEmployeeById(UUID id) {
        Employee employee = employeeRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Employee not found with id: " + id));
        return employeeMapper.toDto(employee);
    }

    public EmployeeDto createEmployee(EmployeeDto employeeDto) {
        Employee employee = employeeMapper.toEntity(employeeDto);
        employee.setDepartment(departmentRepository.findById(employeeDto.getDepartmentId())
                .orElseThrow(() -> new RuntimeException("Department not found with id: " + employeeDto.getDepartmentId())));
        employee.setDesignation(designationRepository.findById(employeeDto.getDesignationId())
                .orElseThrow(() -> new RuntimeException("Designation not found with id: " + employeeDto.getDesignationId())));
        Employee saved = employeeRepository.save(employee);
        return employeeMapper.toDto(saved); //
    }

    public void changeEmployeeDepartment(UUID employeeId, UUID departmentId) {
        Employee employee = employeeRepository.findById(employeeId)
                .orElseThrow(() -> new RuntimeException("Employee not found with id: " + employeeId));
        Department department = departmentRepository.findById(departmentId)
                .orElseThrow(() -> new RuntimeException("Department not found with id: " + departmentId));
        employee.setDepartment(department);
        employeeRepository.save(employee);
    }

    public void changeEmployeeDesignation(UUID employeeId, UUID designationId) {
        Employee employee = employeeRepository.findById(employeeId)
                .orElseThrow(() -> new RuntimeException("Employee not found with id: " + employeeId));
        Designation designation = designationRepository.findById(designationId)
                .orElseThrow(() -> new RuntimeException("Designation not found with id: " + designationId));
        employee.setDesignation(designation);
        employeeRepository.save(employee);
    }

    public EmployeeDto getMyInfo(UUID userId) {
        Employee employee = employeeRepository.findByUserId(userId)
                .orElseThrow(() -> new RuntimeException("Employee not found with user id: " + userId));
        return employeeMapper.toDto(employee);
    }

    public List<EmployeeDto> getEmployeesWithoutDepartment() {
        return employeeRepository.findByDepartmentIsNull().stream()
                .map(employeeMapper::toDto)
                .toList();
    }
}