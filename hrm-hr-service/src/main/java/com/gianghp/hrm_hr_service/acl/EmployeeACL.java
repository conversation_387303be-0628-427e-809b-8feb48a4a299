package com.gianghp.hrm_hr_service.acl;

import com.gianghp.hrm.dtos.EmployeeBasicDto;
import com.gianghp.hrm.enums.EmployeeStatus;
import com.gianghp.hrm_hr_service.entities.Employee;
import com.gianghp.hrm_hr_service.mappers.EmployeeMapper;
import com.gianghp.hrm_hr_service.producers.KafkaHrProducer;
import com.gianghp.hrm_hr_service.repositories.DepartmentRepository;
import com.gianghp.hrm_hr_service.repositories.DesignationRepository;
import com.gianghp.hrm_hr_service.repositories.EmployeeRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class EmployeeACL {
    private final EmployeeRepository employeeRepository;
    private final DesignationRepository designationRepository;
    private final DepartmentRepository departmentRepository;
    private final EmployeeMapper employeeMapper;
    private final KafkaHrProducer kafkaHrProducer;

    public void handleEmployeeCreatedEvent(EmployeeBasicDto employeeBasicDto) {
        if (employeeRepository.existsByUserId(employeeBasicDto.getUserId()) || employeeRepository.existsByEmail(employeeBasicDto.getEmail()) || employeeRepository.existsByEmployeeCode(employeeBasicDto.getEmployeeCode())) {
            log.info("Employee already exists, skipping");
            return;
        }
        try {
            Employee employee = employeeMapper.toEntity(employeeBasicDto);
            employee.setStatus(EmployeeStatus.ACTIVE);
            if (employeeBasicDto.getDesignationId() != null) {
                employee.setDesignation(designationRepository.findById(employeeBasicDto.getDesignationId()).orElseThrow(() -> new RuntimeException("Designation not found with id: " + employeeBasicDto.getDesignationId())));
            } else {
                throw new RuntimeException("Designation must be provided.");
            }
            if (employeeBasicDto.getDepartmentId() != null) {
                employee.setDepartment(departmentRepository.findById(employeeBasicDto.getDepartmentId()).orElseThrow(() -> new RuntimeException("Department not found with id: " + employeeBasicDto.getDepartmentId())));
            } else {
                throw new RuntimeException("Department must be provided.");
            }

            Employee saved = employeeRepository.save(employee);
            employeeBasicDto.setId(saved.getId());
            employeeBasicDto.setDepartmentName(saved.getDepartment().getName());
            employeeBasicDto.setDesignationName(saved.getDesignation().getTitle());

            kafkaHrProducer.sendEmployeeWhenCreated(employeeBasicDto);
            log.info("Employee created {} successfully", employee);
        } catch (Exception e) {
            log.error("Failed to create employee: {}", e.getMessage());
            kafkaHrProducer.sendEmployeeWhenCreateFailed(employeeBasicDto);
            throw e;
        }
    }

    public void handleEmployeeCreateFailedEvent(UUID employeeId) {
        try {
            log.info("Employee create failed, deleting user with id: {}", employeeId);
            employeeRepository.deleteById(employeeId);
        } catch (Exception e) {
            log.error("Failed to delete user: {}", e.getMessage());
        }
    }
}
