package com.gianghp.hrm.configs;

import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.TopicBuilder;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.Map;

@Configuration
public class KafkaConfig {

    @Bean
    public NewTopic hrTopic(){
        return TopicBuilder.name("hr-topic")
                .build();
    }

    @Bean
    public NewTopic timeTopic(){
        return TopicBuilder.name("time-topic")
                .build();
    }


}
