package com.gianghp.hrm.dtos;

import com.gianghp.hrm.enums.Gender;
import com.gianghp.hrm.enums.WorkLocationType;
import com.gianghp.hrm.enums.WorkType;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Locale;
import java.util.UUID;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EmployeeBasicDto {
    private UUID id;
    private UUID userId;
    private String employeeCode;
    private String fullName;
    private String email;
    private String firstName;
    private String lastName;
    private String mobileNumber;
    private LocalDate dateOfBirth;
    private Gender gender;
    private String nationality;
    private String address;
    private String city;
    private String state;
    private String designationName;
    private UUID designationId;
    private UUID departmentId;
    private String departmentName;
    private WorkType type;
    private WorkLocationType workLocationType;
    private LocalDate dateOfJoining;
    private BaseSalaryBasicDto baseSalary;
}
