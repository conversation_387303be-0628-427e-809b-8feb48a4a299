package com.gianghp.hrm.exceptions; // Đ<PERSON>m bảo package này khớp với cấu trúc thư mục của bạn

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Exception tùy chỉnh được ném ra khi yêu cầu từ client không hợp lệ.
 * Thường ánh xạ tới mã trạng thái HTTP 400 Bad Request.
 */
@ResponseStatus(HttpStatus.BAD_REQUEST) // Đánh dấu ngoại lệ này sẽ trả về HTTP 400
public class BadRequestException extends RuntimeException {

    public BadRequestException(String message) {
        super(message);
    }

    public BadRequestException(String message, Throwable cause) {
        super(message, cause);
    }
}