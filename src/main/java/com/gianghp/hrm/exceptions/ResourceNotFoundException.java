package com.gianghp.hrm.exceptions; // Đảm bảo package này khớp với cấu trúc thư mục của bạn

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Exception tùy chỉnh được ném ra khi một tài nguyên không được tìm thấy.
 * Thường ánh xạ tới mã trạng thái HTTP 404 Not Found.
 */
@ResponseStatus(HttpStatus.NOT_FOUND) // Đánh dấu ngoại lệ này sẽ trả về HTTP 404
public class ResourceNotFoundException extends RuntimeException {

    private String resourceName;
    private String fieldName;
    private Object fieldValue;

    public ResourceNotFoundException(String resourceName, String fieldName, Object fieldValue) {
        super(String.format("%s not found with %s : '%s'", resourceName, fieldName, fieldValue));
        this.resourceName = resourceName;
        this.fieldName = fieldName;
        this.fieldValue = fieldValue;
    }

    public String getResourceName() {
        return resourceName;
    }

    public String getFieldName() {
        return fieldName;
    }

    public Object getFieldValue() {
        return fieldValue;
    }
}