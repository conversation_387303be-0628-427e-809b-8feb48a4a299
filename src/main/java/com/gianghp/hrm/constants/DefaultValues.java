package com.gianghp.hrm.constants;

import com.gianghp.hrm.enums.LeaveRequestStatus;
import com.gianghp.hrm.enums.PayrollStatus;
import com.gianghp.hrm.enums.TimeEntryStatus;
import com.gianghp.hrm.enums.UserStatus;

public final class DefaultValues {
    public static final UserStatus DEFAULT_USER_STATUS = UserStatus.ACTIVE; // Nên dùng Enum.name()
    public static final LeaveRequestStatus DEFAULT_LEAVE_REQUEST_STATUS = LeaveRequestStatus.PENDING;
    public static final TimeEntryStatus DEFAULT_TIME_ENTRY_STATUS = TimeEntryStatus.ON_TIME;
    public static final PayrollStatus DEFAULT_PAYROLL_STATUS = PayrollStatus.PENDING;
}