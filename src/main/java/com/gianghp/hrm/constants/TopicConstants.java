package com.gianghp.hrm.constants;

public class TopicConstants {
    public static final String USER_CREATED = "hrm.user.created";
    public static final String USER_WITH_EMPLOYEE_CREATED = "hrm.user.with.employee.created";
    public static final String EMPLOYEE_CREATED = "hrm.employee.created";
    public static final String EMPLOYEE_UPDATED = "hrm.employee.updated";
    public static final String ATTENDANCE_RECORDED = "hrm.attendance.recorded";
    public static final String LEAVE_REQUEST_SUBMITTED = "hrm.leave.request.submitted";
    public static final String PAYROLL_PROCESSED = "hrm.payroll.processed";
    public static final String OVERTIME_REQUEST_SUBMITTED = "hrm.overtime.request.submitted";
    public static final String EMPLOYEE_CREATE_FAILED = "hrm.employee.create.failed";
    public static final String UPDATE_EMPLOYEE_ROLE = "hrm.employee.role.updated";
    public static final String OVERTIME_APPROVED = "hrm.overtime.approved";
    public static final String FINAL_DAILY_ATTENDANCE = "hrm.final.daily.attendance";
    public static final String ATTENDANCE_SETTING_CREATED = "hrm.attendance.setting.created";
}