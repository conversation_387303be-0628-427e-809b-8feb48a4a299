package com.gianghp.hrm.constants;

public final class SystemConstants {
    // <PERSON><PERSON><PERSON> hình bảo mật
    public static final int PASSWORD_MIN_LENGTH = 8;
    public static final int BCRYPT_SALT_ROUNDS = 10; // Cho thuật toán băm mật khẩu như BCrypt

    // C<PERSON><PERSON> hình mặc định cho các chính sách
    public static final double DEFAULT_ANNUAL_LEAVE_DAYS = 12.0; // Nếu không có cấu hình cụ thể
    public static final int DEFAULT_MAX_LEAVE_DAYS_PER_YEAR = 30;

    // <PERSON><PERSON><PERSON> vai trò hệ thống
    public static final String ROLE_ADMIN = "ADMIN";
    public static final String ROLE_EMPLOYEE = "EMPLOYEE";
    public static final String ROLE_MANAGER = "MANAGER";

    // <PERSON><PERSON><PERSON> quyền hệ thống
    public static final String PERMISSION_READ = "READ";
    public static final String PERMISSION_WRITE = "WRITE";
    public static final String PERMISSION_DELETE = "DELETE";
    public static final String PERMISSION_MANAGE_USERS = "MANAGE_USERS";
    // ...
}