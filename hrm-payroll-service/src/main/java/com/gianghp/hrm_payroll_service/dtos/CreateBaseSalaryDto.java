package com.gianghp.hrm_payroll_service.dtos;

import com.gianghp.hrm.enums.CurrencyCode;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreateBaseSalaryDto {
  private UUID employeeId;
  private BigDecimal amount;
  private CurrencyCode currency;
  private LocalDate effectiveDate;
}
