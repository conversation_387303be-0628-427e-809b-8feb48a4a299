package com.gianghp.hrm_payroll_service.dtos;

import com.gianghp.hrm.constants.NumericConstants;
import com.gianghp.hrm.enums.CheckinStatus;
import com.gianghp.hrm.enums.CheckoutStatus;
import com.gianghp.hrm_payroll_service.entities.EmployeeCache;
import jakarta.persistence.Column;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DailySalaryDto {
  private UUID id;

  private LocalDate date;

  private CheckinStatus checkinStatus;

  private CheckoutStatus checkoutStatus;

  private BigDecimal standardWorkingHours;

  private BigDecimal overtimeHours;

  private BigDecimal totalLateDeductionRate;

  private BigDecimal totalEarlyOutDeductionRate;

  private BigDecimal overtimeRate;

  private BigDecimal amount;

  private String note;
}
