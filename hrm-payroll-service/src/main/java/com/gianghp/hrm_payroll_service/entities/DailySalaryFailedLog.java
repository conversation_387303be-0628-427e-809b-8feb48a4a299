package com.gianghp.hrm_payroll_service.entities;

import com.gianghp.hrm.entities.BaseEntity;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDate;
import java.util.UUID;

@Entity
@Table(name = "daily_salary_failed_log")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class DailySalaryFailedLog extends BaseEntity {

  @OneToOne
  @JoinColumn(name = "attendance_id", nullable = false)
  private AttendanceForPayrollCache attendance;

  @Column(name = "attendance_date", nullable = false)
  private LocalDate attendanceDate;

  @Column(name = "reason", columnDefinition = "TEXT")
  private String reason;

  @Column(name = "resolved", nullable = false)
  private boolean resolved;
}
