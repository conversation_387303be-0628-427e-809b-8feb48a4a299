package com.gianghp.hrm_payroll_service.entities;

import com.gianghp.hrm.entities.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.time.LocalDate;

@Entity
@Table(name = "employee_allowance")
public class EmployeeAllowance extends BaseEntity {

  @ManyToOne
  private EmployeeCache employee;

  @ManyToOne
  private Allowance allowance;

  @Column(name = "effective_date", nullable = false)
  private LocalDate effectiveDate;
}
