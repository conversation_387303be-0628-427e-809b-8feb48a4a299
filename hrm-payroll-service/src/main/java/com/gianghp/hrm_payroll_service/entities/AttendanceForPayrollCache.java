package com.gianghp.hrm_payroll_service.entities;

import com.gianghp.hrm.constants.NumericConstants;
import com.gianghp.hrm.enums.CheckinStatus;
import com.gianghp.hrm.enums.CheckoutStatus;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "attendance_for_payroll_cache")
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AttendanceForPayrollCache {
  @Id
  private UUID id;

  @ManyToOne
  @JoinColumn(name = "employee_id", nullable = false)
  private EmployeeCache employeeCache;

  @Column(name = "date", nullable = false)
  private LocalDate date;

  @Enumerated(EnumType.STRING)
  @Column(name = "checkin_status", length = 20)
  private CheckinStatus checkinStatus;

  @Enumerated(EnumType.STRING)
  @Column(name = "checkout_status", length = 20)
  private CheckoutStatus checkoutStatus;

  @Column(name = "standard_working_hours", precision = NumericConstants.DURATION_PRECISION, scale = NumericConstants.DURATION_SCALE)
  private BigDecimal standardWorkingHours;

  @Column(name = "overtime_hours", precision = NumericConstants.DURATION_PRECISION, scale = NumericConstants.DURATION_SCALE)
  private BigDecimal overtimeHours;

  @Column(name = "total_late_deduction_rate", precision = NumericConstants.DURATION_PRECISION, scale = NumericConstants.DURATION_SCALE)
  private BigDecimal totalLateDeductionRate;

  @Column(name = "total_early_out_deduction_rate", precision = NumericConstants.DURATION_PRECISION, scale = NumericConstants.DURATION_SCALE)
  private BigDecimal totalEarlyOutDeductionRate;

  @Column(name = "overtime_rate", precision = NumericConstants.DURATION_PRECISION, scale = NumericConstants.DURATION_SCALE)
  private BigDecimal overtimeRate;

}
