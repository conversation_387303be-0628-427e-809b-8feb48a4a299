package com.gianghp.hrm_payroll_service.entities;

import com.gianghp.hrm.constants.NumericConstants;
import com.gianghp.hrm.entities.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "allowance")
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class Allowance extends BaseEntity {

  @Column(nullable = false, unique = true)
  private String name;

  @Column(nullable = false)
  private String description;

  @Column(name = "default_amount", nullable = false, precision = NumericConstants.SALARY_PRECISION, scale = NumericConstants.SALARY_SCALE)
  private BigDecimal amount;
}
