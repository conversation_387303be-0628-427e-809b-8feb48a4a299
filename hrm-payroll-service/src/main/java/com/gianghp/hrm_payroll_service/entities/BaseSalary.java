package com.gianghp.hrm_payroll_service.entities;

import com.gianghp.hrm.entities.BaseEntity;
import com.gianghp.hrm.enums.CurrencyCode;
import com.gianghp.hrm_payroll_service.entities.EmployeeCache;
import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

@Entity
@Table(name = "base_salary")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BaseSalary extends BaseEntity {
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "employee_id", nullable = false)
    private EmployeeCache employee;

    // <PERSON><PERSON><PERSON> cơ bản
    @Column(name = "amount", nullable = false)
    private BigDecimal amount;

    // <PERSON>ày bắt đầu có hiệu lực
    @Column(name = "effective_date", nullable = false)
    private LocalDate effectiveDate;

    @Column(name = "currency", nullable = false, length = 3)
    @Enumerated(EnumType.STRING)
    private CurrencyCode currency = CurrencyCode.VND; // default
}
