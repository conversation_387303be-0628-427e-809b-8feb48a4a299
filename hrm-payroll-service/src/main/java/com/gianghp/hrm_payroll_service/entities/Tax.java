package com.gianghp.hrm_payroll_service.entities;

import com.gianghp.hrm.entities.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.time.LocalDate;
import lombok.*;

@Entity
@Table(name = "tax")
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class Tax extends BaseEntity {

  @Column(nullable = false, unique = true)
  private String name;

  @Column(nullable = false)
  private Double rate; // Ví dụ: 0.05 cho 5%

  @Column(name = "effective_date", nullable = false)
  private LocalDate effectiveDate;
}
