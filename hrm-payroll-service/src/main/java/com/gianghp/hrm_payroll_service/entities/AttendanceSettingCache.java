package com.gianghp.hrm_payroll_service.entities;

import com.gianghp.hrm.constants.NumericConstants;
import com.gianghp.hrm.enums.WorkType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "attendance_setting_cache")
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AttendanceSettingCache {
  @Id
  private UUID id;

  @Column(name = "min_working_minutes", nullable = false)
  private int minWorkingMinutes; // ví dụ: 240 phút (4 giờ)

  @Column(name = "standard_working_hours", nullable = false, precision = NumericConstants.DURATION_PRECISION, scale = NumericConstants.DURATION_SCALE)
  private BigDecimal standardWorkingHours; // ví dụ: 480 phút (8 giờ)

  @Enumerated(EnumType.STRING)
  @Column(name = "work_type", nullable = false, length = 20)
  private WorkType workType;

  @Column(name = "effective_date", nullable = false)
  private LocalDate effectiveDate;
}
