package com.gianghp.hrm_payroll_service.entities;

import com.gianghp.hrm.constants.NumericConstants;
import com.gianghp.hrm.enums.CurrencyCode;
import com.gianghp.hrm.enums.WorkType;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.*;

import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Table(name = "employee_cache")
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EmployeeCache {
    @Id
    @Column(name = "id")
    private UUID id;

    @Column(name = "user_id", nullable = false, unique = true)
    private UUID userId;

    @Column(name = "employee_code")
    private String employeeCode;

    @Column(name = "full_name")
    private String fullName;

    @Column(name = "designation_name")
    private String designationName;

    @Column(name = "work_type", nullable = false, length = 20)
    @Enumerated(EnumType.STRING)
    private WorkType workType;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @PreUpdate
    @PrePersist
    public void updateTimestamp() {
        this.updatedAt = LocalDateTime.now();
    }
}
