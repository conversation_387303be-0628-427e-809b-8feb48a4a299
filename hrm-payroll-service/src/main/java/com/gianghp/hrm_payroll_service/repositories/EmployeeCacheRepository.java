package com.gianghp.hrm_payroll_service.repositories;

import com.gianghp.hrm_payroll_service.entities.EmployeeCache;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.UUID;

@Repository
public interface EmployeeCacheRepository extends JpaRepository<EmployeeCache, UUID> {

  boolean existsByEmployeeCode(String employeeCode);
}
