package com.gianghp.hrm_payroll_service.repositories;

import com.gianghp.hrm_payroll_service.entities.DailySalary;
import java.time.LocalDate;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface DailySalaryRepository extends JpaRepository<DailySalary, UUID> {
  Page<DailySalary> findByAttendance_EmployeeCache_IdAndAttendance_DateBetween(UUID employeeId, LocalDate startDate, LocalDate endDate, Pageable pageable);
}
