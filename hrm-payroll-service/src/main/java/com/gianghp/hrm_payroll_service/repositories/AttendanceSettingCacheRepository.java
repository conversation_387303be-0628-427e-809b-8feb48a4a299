package com.gianghp.hrm_payroll_service.repositories;

import com.gianghp.hrm_payroll_service.entities.AttendanceSettingCache;
import java.time.LocalDate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.UUID;

@Repository
public interface AttendanceSettingCacheRepository extends  JpaRepository<AttendanceSettingCache, UUID> {

  AttendanceSettingCache findTopByEffectiveDateLessThanEqualOrderByEffectiveDateDesc(LocalDate localDate);
}
