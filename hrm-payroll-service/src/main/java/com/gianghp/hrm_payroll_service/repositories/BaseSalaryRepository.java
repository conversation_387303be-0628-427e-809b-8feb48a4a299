package com.gianghp.hrm_payroll_service.repositories;

import com.gianghp.hrm_payroll_service.entities.BaseSalary;
import java.time.LocalDate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.UUID;

@Repository
public interface BaseSalaryRepository extends JpaRepository<BaseSalary, UUID> {
    Page<BaseSalary> findAllByEmployee_Id(UUID employeeId, Pageable pageable);

  BaseSalary findTopByEmployee_IdAndEffectiveDateLessThanEqualOrderByEffectiveDateDesc(UUID id,
      LocalDate localDate);
}
