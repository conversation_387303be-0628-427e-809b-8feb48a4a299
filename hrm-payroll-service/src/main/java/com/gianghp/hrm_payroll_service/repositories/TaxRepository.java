package com.gianghp.hrm_payroll_service.repositories;

import com.gianghp.hrm_payroll_service.entities.Tax;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface TaxRepository extends JpaRepository<Tax, UUID> {

  List<Tax> findByEffectiveDateLessThanEqualOrderByEffectiveDateDesc(LocalDate localDate);
}
