package com.gianghp.hrm_payroll_service.repositories;

import com.gianghp.hrm_payroll_service.entities.AttendanceForPayrollCache;
import java.time.LocalDate;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface AttendanceForPayrollCacheRepository extends JpaRepository<AttendanceForPayrollCache, UUID> {

  AttendanceForPayrollCache findByEmployeeCache_IdAndDate(UUID id, LocalDate localDate);
}
