package com.gianghp.hrm_payroll_service.acl;

import com.gianghp.hrm.dtos.AttendanceBasicDto;
import com.gianghp.hrm.dtos.AttendanceBasicWrapperDto;
import com.gianghp.hrm.dtos.AttendanceSettingBasicDto;
import com.gianghp.hrm.dtos.EmployeeBasicDto;
import com.gianghp.hrm_payroll_service.entities.AttendanceForPayrollCache;
import com.gianghp.hrm_payroll_service.entities.EmployeeCache;
import com.gianghp.hrm_payroll_service.mappers.AttendanceForPayrollCacheMapper;
import com.gianghp.hrm_payroll_service.mappers.EmployeeCacheMapper;
import com.gianghp.hrm_payroll_service.repositories.AttendanceForPayrollCacheRepository;
import com.gianghp.hrm_payroll_service.repositories.EmployeeCacheRepository;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class AttendanceACL {

  private final EmployeeCacheRepository employeeCacheRepository;
  private final AttendanceForPayrollCacheRepository attendanceForPayrollCacheRepository;
  private final AttendanceForPayrollCacheMapper attendanceForPayrollCacheMapper;

  public void handleFinalDailyAttendanceEvent(AttendanceBasicWrapperDto message) {
    log.info("Handling final daily attendance event: {}", message);
    List<AttendanceForPayrollCache> toSave = message.getAttendances().stream()
        .map(attendanceBasicDto -> {
          AttendanceForPayrollCache attendanceForPayrollCache = attendanceForPayrollCacheMapper.toEntity(
              attendanceBasicDto);

          UUID employeeId = attendanceBasicDto.getEmployeeId();
          Optional<EmployeeCache> employeeCacheOptional = employeeCacheRepository.findById(employeeId);

          if (employeeCacheOptional.isEmpty()) {
            log.error("Employee not found for employeeId: {} in attendanceBasicDto: {}",
                employeeId, attendanceBasicDto); // Log cả DTO để có thêm ngữ cảnh
            throw new RuntimeException("Employee not found for ID: " + employeeId);
          }

          attendanceForPayrollCache.setEmployeeCache(employeeCacheOptional.get());
          return attendanceForPayrollCache;
        })
        .toList();

    log.info("Saving {} attendance records for payroll", toSave.size());
    attendanceForPayrollCacheRepository.saveAll(toSave);
    attendanceForPayrollCacheRepository.flush();
  }
}
