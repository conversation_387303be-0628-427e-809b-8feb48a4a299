package com.gianghp.hrm_payroll_service.acl;

import com.gianghp.hrm.dtos.ApprovedOvertimeBasicDto;
import com.gianghp.hrm_payroll_service.mappers.OvertimeForPayrollCacheMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class OvertimeACL {

//  private final OvertimeForPayrollCacheMapper overtimeForPayrollCacheMapper;
//  private final OvertimeForPayrollCacheRepository overtimeForPayrollCacheRepository;
//
//  public void handleOvertimeApprovedEvent(ApprovedOvertimeBasicDto message) {
//    try {
//      OvertimeForPayrollCache overtimeForPayrollCache = overtimeForPayrollCacheMapper.toEntity(
//          message);
//      overtimeForPayrollCacheRepository.save(overtimeForPayrollCache);
//      log.info("Overtime for payroll cache created {} successfully", overtimeForPayrollCache);
//    } catch (Exception e) {
//      log.error("Failed to create overtime for payroll cache: {}", e.getMessage());
//      // Could send failure event back if needed
//    }
//  }
}
