package com.gianghp.hrm_payroll_service.acl;

import com.gianghp.hrm.dtos.AttendanceSettingBasicDto;
import com.gianghp.hrm_payroll_service.entities.AttendanceSettingCache;
import com.gianghp.hrm_payroll_service.mappers.AttendanceSettingCacheMapper;
import com.gianghp.hrm_payroll_service.repositories.AttendanceSettingCacheRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class AttendanceSettingACL {

  private final AttendanceSettingCacheRepository attendanceSettingCacheRepository;
  private final AttendanceSettingCacheMapper attendanceSettingCacheMapper;

  public void handleAttendanceSettingCreatedEvent(AttendanceSettingBasicDto message) {
    attendanceSettingCacheMapper.toEntity(message);
    attendanceSettingCacheRepository.save(attendanceSettingCacheMapper.toEntity(message));
  }
}
