package com.gianghp.hrm_payroll_service.services;

import com.gianghp.hrm.enums.CheckinStatus;
import com.gianghp.hrm.enums.CheckoutStatus;
import com.gianghp.hrm.enums.DayType;
import com.gianghp.hrm.utils.DateTimeUtils;
import com.gianghp.hrm_payroll_service.dtos.DailySalaryDto;
import com.gianghp.hrm_payroll_service.entities.AttendanceForPayrollCache;
import com.gianghp.hrm_payroll_service.entities.AttendanceSettingCache;
import com.gianghp.hrm_payroll_service.entities.BaseSalary;
import com.gianghp.hrm_payroll_service.entities.DailySalary;
import com.gianghp.hrm_payroll_service.entities.DailySalaryFailedLog;
import com.gianghp.hrm_payroll_service.entities.EmployeeCache;
import com.gianghp.hrm_payroll_service.entities.Tax;
import com.gianghp.hrm_payroll_service.mappers.DailySalaryMapper;
import com.gianghp.hrm_payroll_service.repositories.AttendanceForPayrollCacheRepository;
import com.gianghp.hrm_payroll_service.repositories.AttendanceSettingCacheRepository;
import com.gianghp.hrm_payroll_service.repositories.BaseSalaryRepository;
import com.gianghp.hrm_payroll_service.repositories.DailySalaryFailedLogRepository;
import com.gianghp.hrm_payroll_service.repositories.DailySalaryRepository;
import com.gianghp.hrm_payroll_service.repositories.EmployeeCacheRepository;
import com.gianghp.hrm_payroll_service.repositories.TaxRepository;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class DailySalaryService {

  private final BaseSalaryRepository baseSalaryRepository;
  private final TaxRepository taxRepository;
  private final DailySalaryRepository dailySalaryRepository;
  private final EmployeeCacheRepository employeeCacheRepository;
  private final AttendanceForPayrollCacheRepository attendanceForPayrollCacheRepository;
  private final AttendanceSettingCacheRepository attendanceSettingCacheRepository;
  private final DailySalaryFailedLogRepository failedLogRepository;
  private final DailySalaryMapper dailySalaryMapper;

  public Page<DailySalaryDto> findByEmployeeIdAndMonth(UUID employeeId, YearMonth yearMonth, Pageable pageable) {
    LocalDate startDate = yearMonth.atDay(1);
    LocalDate endDate = yearMonth.atEndOfMonth();
    return dailySalaryRepository.findByAttendance_EmployeeCache_IdAndAttendance_DateBetween(employeeId, startDate, endDate, pageable).map(dailySalaryMapper::toDto);
  }

  public void caculateDailySalaryForAllEmployees(LocalDate localDate) {

    DayOfWeek dayOfWeek = localDate.getDayOfWeek();

    if (dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY) {
      System.out.println("Skipping salary calculation for weekend: " + localDate);
      return;
    }

    List<DailySalary> dailySalaries = new ArrayList<>();
    AttendanceSettingCache attendanceSettingCache = attendanceSettingCacheRepository.findTopByEffectiveDateLessThanEqualOrderByEffectiveDateDesc(
        localDate);

    YearMonth yearMonth = YearMonth.from(localDate);
    int daysInMonth = DateTimeUtils.calculateWorkingDaysInMonth(yearMonth);

    List<EmployeeCache> allEmployees = employeeCacheRepository.findAll();
    List<Tax> currentTaxes = taxRepository.findByEffectiveDateLessThanEqualOrderByEffectiveDateDesc(
        localDate);

    for (EmployeeCache employee : allEmployees) {

      AttendanceForPayrollCache attendance = attendanceForPayrollCacheRepository.findByEmployeeCache_IdAndDate(
          employee.getId(), localDate);

      if (attendance == null) {
        log.warn("Attendance not found for employee {} on {}", employee.getId(), localDate);
        continue;
      }

      try {
        BaseSalary baseSalary = baseSalaryRepository.findTopByEmployee_IdAndEffectiveDateLessThanEqualOrderByEffectiveDateDesc(
            employee.getId(), localDate
        );

        if (baseSalary == null) {
          log.warn("Base salary not found for employee {} on {}", employee.getId(), localDate);
          throw new RuntimeException("Base salary not found for employee " + employee.getEmployeeCode());
        }


        DailySalary dailySalary = new DailySalary();
        dailySalary.setAttendance(attendance);

        BigDecimal dailyBaseSalary = baseSalary.getAmount()
            .divide(BigDecimal.valueOf(daysInMonth), 2, RoundingMode.HALF_UP);

        if (attendance.getCheckinStatus() == CheckinStatus.HOLIDAY) {
          dailySalary.setAmount(dailyBaseSalary);
          dailySalary.setNote("Holiday");
          dailySalaries.add(dailySalary);
          continue;
        }


        String note = "checkin status: " + attendance.getCheckinStatus() + ", checkout status: "
            + ", standard working hours: "
            + attendance.getStandardWorkingHours()
            + attendance.getCheckoutStatus() + ", late deduction rate: "
            + attendance.getTotalLateDeductionRate() + ", early out deduction rate: "
            + attendance.getTotalEarlyOutDeductionRate() + ", overtime hours: "
            + attendance.getOvertimeHours() + ", overtime rate: " + attendance.getOvertimeRate() ;

        if (attendance.getStandardWorkingHours()
            .compareTo(BigDecimal.valueOf(attendanceSettingCache.getMinWorkingMinutes() / 60))
            < 0) {
          dailySalary.setAmount(BigDecimal.ZERO);
          dailySalary.setNote(note);
          dailySalaries.add(dailySalary);
          continue;
        }

        BigDecimal hourlySalary = dailyBaseSalary.divide(
            attendanceSettingCache.getStandardWorkingHours(), 2, RoundingMode.HALF_UP);

        BigDecimal totalDeduction = dailyBaseSalary.multiply(
            attendance.getTotalLateDeductionRate().add(attendance.getTotalEarlyOutDeductionRate()));

        BigDecimal totalAmountWithoutTaxAndDeduction = dailyBaseSalary.add(
            attendance.getOvertimeHours().multiply(hourlySalary)
                .multiply(attendance.getOvertimeRate()));

        BigDecimal totalAmountWithoutTax = totalAmountWithoutTaxAndDeduction.subtract(
            totalDeduction);

        BigDecimal finalAmount = totalAmountWithoutTax.subtract(
            totalAmountWithoutTax.multiply(
                BigDecimal.valueOf(currentTaxes.stream().mapToDouble(Tax::getRate).sum()))
        );


        dailySalary.setAmount(finalAmount);
        dailySalary.setNote(note);

        dailySalaries.add(dailySalary);

      } catch (Exception ex) {
        log.error("Failed to calculate daily salary for employee {} on {}: {}",
            employee.getId(), localDate, ex.getMessage(), ex);

        failedLogRepository.save(
            DailySalaryFailedLog.builder()
                .attendance(attendance)
                .attendanceDate(attendance.getDate())
                .reason(ex.getMessage())
                .resolved(false)
                .build()
        );
      }
    }

    dailySalaryRepository.saveAll(dailySalaries);
  }



}



