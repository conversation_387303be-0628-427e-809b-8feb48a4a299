package com.gianghp.hrm_payroll_service.services;

import com.gianghp.hrm_payroll_service.dtos.BaseSalaryDto;
import com.gianghp.hrm_payroll_service.dtos.CreateBaseSalaryDto;
import com.gianghp.hrm_payroll_service.mappers.BaseSalaryMapper;
import com.gianghp.hrm_payroll_service.repositories.BaseSalaryRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
@RequiredArgsConstructor
public class BaseSalaryService {
    private final BaseSalaryRepository baseSalaryRepository;
    private final BaseSalaryMapper baseSalaryMapper;

    public Page<BaseSalaryDto> getBaseSalaryByEmployee(UUID employeeId, Pageable pageable) {
        return baseSalaryRepository.findAllByEmployee_Id(employeeId, pageable).map(baseSalaryMapper::toDto);
    }

    public BaseSalaryDto createBaseSalary(UUID employeeId, CreateBaseSalaryDto createBaseSalaryDto) {
        return null;
    }
}
