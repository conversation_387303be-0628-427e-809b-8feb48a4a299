package com.gianghp.hrm_payroll_service.schedulers;

import com.gianghp.hrm_payroll_service.services.PayrollService;
import java.time.LocalDate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class PayrollScheduler {

//  private final PayrollService payrollService;
//
//  public PayrollScheduler(PayrollService payrollService) {
//    this.payrollService = payrollService;
//  }
//
//  @Scheduled(cron = "0 0 0 * * ?") // Chạy lúc 00:00 mỗi ngày
//  public void calculateDailyPayroll() {
//    payrollService.calculateAllEmployeesForDate(LocalDate.now().minusDays(1));
//  }

}
