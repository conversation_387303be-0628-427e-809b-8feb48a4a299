package com.gianghp.hrm_payroll_service.configs;


import com.gianghp.hrm_payroll_service.entities.Tax;
import com.gianghp.hrm_payroll_service.repositories.TaxRepository;
import jakarta.annotation.PostConstruct;
import java.time.LocalDate;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class DataInitialize {

  private final TaxRepository taxRepository;

  @PostConstruct
  public void init() {
    if (taxRepository.count() == 0) {
      taxRepository.save(new Tax("Personal Income Tax", 0.05, LocalDate.now().minusYears(1)));
      taxRepository.save(new Tax("Health Insurance", 0.015, LocalDate.now().minusYears(1)));
      taxRepository.save(new Tax("Social Insurance", 0.08, LocalDate.now().minusYears(1)));
      taxRepository.save(new Tax("Unemployment Insurance", 0.01, LocalDate.now().minusYears(1)));
    }
  }
}

