package com.gianghp.hrm_payroll_service.controllers;

import com.gianghp.hrm.dtos.ApiResponse;
import com.gianghp.hrm_payroll_service.dtos.BaseSalaryDto;
import com.gianghp.hrm_payroll_service.dtos.CreateBaseSalaryDto;
import com.gianghp.hrm_payroll_service.services.BaseSalaryService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/base-salaries")
@RequiredArgsConstructor
public class BaseSalaryController {

  private final BaseSalaryService baseSalaryService;

  @GetMapping("/employee/{employeeId}")
  public ResponseEntity<ApiResponse<List<BaseSalaryDto>>> getBaseSalaryByEmployee(
      @PathVariable UUID employeeId,
      @RequestParam(defaultValue = "0") int page,
      @RequestParam(defaultValue = "10") int size,
      @RequestParam(defaultValue = "effectiveDate") String sortBy,
      @RequestParam(defaultValue = "desc") String sortDir
  ) {
    try {
      Pageable pageable = PageRequest.of(page, size, sortDir.equalsIgnoreCase("desc")
          ? Sort.by(sortBy).descending()
          : Sort.by(sortBy).ascending());
      Page<BaseSalaryDto> baseSalary = baseSalaryService.getBaseSalaryByEmployee(employeeId,
          pageable);
      return ResponseEntity.ok(
          ApiResponse.success("Base salary retrieved successfully", baseSalary.getContent(),
              baseSalary.getTotalElements(), baseSalary.getTotalPages(), baseSalary.getNumber()));
    } catch (Exception e) {
      return ResponseEntity.badRequest()
          .body(ApiResponse.error("Failed to retrieve base salary: " + e.getMessage()));
    }
  }

  @PostMapping("/employee/{employeeId}")
  public ResponseEntity<ApiResponse<BaseSalaryDto>> createBaseSalary(
      @PathVariable UUID employeeId,
      @RequestBody CreateBaseSalaryDto createBaseSalaryDto
  ) {
    try {
      BaseSalaryDto createdBaseSalary = baseSalaryService.createBaseSalary(employeeId,
          createBaseSalaryDto);
      return ResponseEntity.ok(ApiResponse.success("Base salary created successfully", createdBaseSalary));
    } catch (Exception e) {
      return ResponseEntity.badRequest()
          .body(ApiResponse.error("Failed to create base salary: " + e.getMessage()));
    }
  }

}
