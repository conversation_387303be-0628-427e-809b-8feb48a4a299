package com.gianghp.hrm_payroll_service.controllers;

import com.gianghp.hrm.dtos.ApiResponse;
import com.gianghp.hrm_payroll_service.dtos.BaseSalaryDto;
import com.gianghp.hrm_payroll_service.dtos.DailySalaryDto;
import com.gianghp.hrm_payroll_service.services.BaseSalaryService;
import com.gianghp.hrm_payroll_service.services.DailySalaryService;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/payroll")
@RequiredArgsConstructor
public class PayrollController {

  private final DailySalaryService dailySalaryService;
  private final BaseSalaryService baseSalaryService;

  @GetMapping("/employee/{employeeId}/daily/{yearMonth}")
  public ResponseEntity<ApiResponse<List<DailySalaryDto>>> getDailySalaryByEmployee(
      @PathVariable UUID employeeId,
      @PathVariable YearMonth yearMonth, @RequestParam(defaultValue = "0") int page,
      @RequestParam(defaultValue = "10") int size,
      @RequestParam(defaultValue = "attendance.date") String sortBy,
      @RequestParam(defaultValue = "desc") String sortDir
  ) {
    try {
      if (yearMonth == null) {
        yearMonth = YearMonth.now();
      }
      Pageable pageable = PageRequest.of(page, size, sortDir.equalsIgnoreCase("desc")
          ? Sort.by(sortBy).descending()
          : Sort.by(sortBy).ascending());
      Page<DailySalaryDto> dailySalary = dailySalaryService.findByEmployeeIdAndMonth(employeeId,
          yearMonth, pageable);
      return ResponseEntity.ok(ApiResponse.success("Daily salary retrieved successfully",
          dailySalary.getContent(), dailySalary.getTotalElements(), dailySalary.getTotalPages(),
          dailySalary.getNumber()));
    } catch (Exception e) {
      return ResponseEntity.status(HttpStatus.BAD_REQUEST)
          .body(ApiResponse.error("Failed to retrieve daily salary: " + e.getMessage()));
    }
  }

  @GetMapping("/employee/{employeeId}/base")
  public ResponseEntity<ApiResponse<List<BaseSalaryDto>>> getBaseSalaryByEmployee(
      @PathVariable UUID employeeId,
      @RequestParam(defaultValue = "0") int page,
      @RequestParam(defaultValue = "10") int size,
      @RequestParam(defaultValue = "effectiveDate") String sortBy,
      @RequestParam(defaultValue = "desc") String sortDir
  ) {
    try {
      Pageable pageable = PageRequest.of(page, size, sortDir.equalsIgnoreCase("desc")
          ? Sort.by(sortBy).descending()
          : Sort.by(sortBy).ascending());
      Page<BaseSalaryDto> baseSalary = baseSalaryService.getBaseSalaryByEmployee(employeeId,
          pageable);
      return ResponseEntity.ok(
          ApiResponse.success("Base salary retrieved successfully", baseSalary.getContent(),
              baseSalary.getTotalElements(), baseSalary.getTotalPages(), baseSalary.getNumber()));

    } catch (Exception e) {
      return ResponseEntity.status(HttpStatus.BAD_REQUEST)
          .body(ApiResponse.error("Failed to retrieve base salary: " + e.getMessage()));
    }
  }


}
