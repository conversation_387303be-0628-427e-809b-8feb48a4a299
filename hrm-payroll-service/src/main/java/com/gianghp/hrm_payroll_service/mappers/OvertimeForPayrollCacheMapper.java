package com.gianghp.hrm_payroll_service.mappers;

import com.gianghp.hrm.dtos.ApprovedOvertimeBasicDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface OvertimeForPayrollCacheMapper {
  OvertimeForPayrollCacheMapper INSTANCE = Mappers.getMapper(OvertimeForPayrollCacheMapper.class);

//  OvertimeForPayrollCache toEntity(ApprovedOvertimeBasicDto dto);
}
