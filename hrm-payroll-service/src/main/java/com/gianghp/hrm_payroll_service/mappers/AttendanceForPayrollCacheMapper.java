package com.gianghp.hrm_payroll_service.mappers;

import com.gianghp.hrm.dtos.AttendanceBasicDto;
import com.gianghp.hrm_payroll_service.entities.AttendanceForPayrollCache;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface AttendanceForPayrollCacheMapper {
    AttendanceForPayrollCacheMapper INSTANCE = Mappers.getMapper(AttendanceForPayrollCacheMapper.class);

    @Mapping(target = "employeeCache", ignore = true)
    AttendanceForPayrollCache toEntity(AttendanceBasicDto dto);
}
