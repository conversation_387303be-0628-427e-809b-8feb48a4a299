package com.gianghp.hrm_payroll_service.mappers;

import com.gianghp.hrm.dtos.EmployeeBasicDto;
import com.gianghp.hrm_payroll_service.entities.EmployeeCache;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.time.LocalDateTime;

@Mapper(componentModel = "spring")
public interface EmployeeCacheMapper {

    @Mapping(target = "updatedAt", expression = "java(java.time.LocalDateTime.now())")
    @Mapping(target = "workType", source = "type")
    EmployeeCache toEntity(EmployeeBasicDto dto);
}
