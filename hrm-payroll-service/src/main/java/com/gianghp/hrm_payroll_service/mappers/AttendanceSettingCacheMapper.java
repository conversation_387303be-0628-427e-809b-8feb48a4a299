package com.gianghp.hrm_payroll_service.mappers;

import com.gianghp.hrm.dtos.AttendanceSettingBasicDto;
import com.gianghp.hrm_payroll_service.entities.AttendanceSettingCache;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface AttendanceSettingCacheMapper {
  AttendanceSettingCacheMapper INSTANCE = Mappers.getMapper(AttendanceSettingCacheMapper.class);

  AttendanceSettingCache toEntity(AttendanceSettingBasicDto dto);
}
