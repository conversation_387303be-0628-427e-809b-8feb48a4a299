package com.gianghp.hrm_payroll_service.producers;

import com.gianghp.hrm.constants.TopicConstants;
import com.gianghp.hrm.dtos.ApprovedOvertimeBasicDto;
import com.gianghp.hrm.dtos.AttendanceBasicDto;
import com.gianghp.hrm.dtos.EmployeeBasicDto;
import com.gianghp.hrm.dtos.EmployeeCreateFailedDto;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class KafkaPayrollProducer {

  private final KafkaTemplate<String, EmployeeCreateFailedDto> kafkaCreateFailedTemplate;

  public void sendEmployeeWhenCreatedFailed(EmployeeBasicDto employee) {
    EmployeeCreateFailedDto employeeCreateFailedDto = new EmployeeCreateFailedDto();
    employeeCreateFailedDto.setEmployeeId(employee.getId());
    employeeCreateFailedDto.setUserId(employee.getUserId());
    Message<EmployeeCreateFailedDto> msg = MessageBuilder.withPayload(
        employeeCreateFailedDto
    ).setHeader(KafkaHeaders.TOPIC, TopicConstants.EMPLOYEE_CREATE_FAILED).build();
    log.info("Sent to {}: {}", TopicConstants.EMPLOYEE_CREATE_FAILED, msg);
    kafkaCreateFailedTemplate.send(msg);
  }

}
