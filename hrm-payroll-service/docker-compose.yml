version: '3.8'
services:
  payroll-service:
    build:
      context: .
      dockerfile: Dockerfile.dev
      target: development
    container_name: payroll-service
    ports:
      - "6040:6040"
    develop:
      watch:
        - action: rebuild
          path: .
    networks:
      - internal
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.payroll.rule=PathPrefix(`/payroll-service`)"
      - "traefik.http.middlewares.payroll-stripprefix.stripprefix.prefixes=/payroll-service"
      - "traefik.http.routers.payroll.middlewares=payroll-stripprefix"
      - "traefik.http.routers.payroll.entrypoints=web"
      - "traefik.http.services.payroll.loadbalancer.server.port=6040"

      - "traefik.http.services.payroll.loadbalancer.healthcheck.path=/health"
      - "traefik.http.services.payroll.loadbalancer.healthcheck.interval=10s"
      - "traefik.http.services.payroll.loadbalancer.healthcheck.timeout=3s"

networks:
  internal:
    external: true
