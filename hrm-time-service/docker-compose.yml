version: '3.8'
services:
  time-service:
    build:
      context: .
      dockerfile: Dockerfile.dev
      target: development
    container_name: time-service
    ports:
      - "6010:6010"
    develop:
      watch:
        - action: rebuild
          path: .
    networks:
      - internal
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.time.rule=PathPrefix(`/time-service`)"
      - "traefik.http.middlewares.time-stripprefix.stripprefix.prefixes=/time-service"
      - "traefik.http.routers.time.middlewares=time-stripprefix"
      - "traefik.http.routers.time.entrypoints=web"
      - "traefik.http.services.time.loadbalancer.server.port=6010"

      - "traefik.http.services.time.loadbalancer.healthcheck.path=/health"
      - "traefik.http.services.time.loadbalancer.healthcheck.interval=10s"
      - "traefik.http.services.time.loadbalancer.healthcheck.timeout=3s"

networks:
  internal:
    external: true
