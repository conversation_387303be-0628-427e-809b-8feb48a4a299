package com.gianghp.hrm_time_service;

import com.gianghp.hrm.enums.CheckinStatus;
import com.gianghp.hrm.enums.CheckoutStatus;
import com.gianghp.hrm_time_service.entities.Attendance;
import com.gianghp.hrm_time_service.entities.EmployeeCache;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
public class AttendanceStatusSplitTest {

    @Test
    public void testAttendanceWithSplitStatus() {
        // Create a test attendance record
        Attendance attendance = new Attendance();
        attendance.setDate(LocalDate.now());
        attendance.setCheckIn(LocalTime.of(8, 0));
        attendance.setCheckOut(LocalTime.of(17, 30));
        attendance.setCheckinStatus(CheckinStatus.ON_TIME);
        attendance.setCheckoutStatus(CheckoutStatus.ON_TIME);
        attendance.setStandardWorkingHours(new BigDecimal("8.0"));
        attendance.setOvertimeHours(new BigDecimal("0.5"));
        attendance.setAutoCheckout(false);

        // Verify the status fields are set correctly
        assertEquals(CheckinStatus.ON_TIME, attendance.getCheckinStatus());
        assertEquals(CheckoutStatus.ON_TIME, attendance.getCheckoutStatus());
        assertNotNull(attendance.getCheckIn());
        assertNotNull(attendance.getCheckOut());
    }

    @Test
    public void testLateCheckinEarlyCheckout() {
        Attendance attendance = new Attendance();
        attendance.setDate(LocalDate.now());
        attendance.setCheckIn(LocalTime.of(8, 30));
        attendance.setCheckOut(LocalTime.of(16, 30));
        attendance.setCheckinStatus(CheckinStatus.LATE);
        attendance.setCheckoutStatus(CheckoutStatus.EARLY_OUT);
        attendance.setStandardWorkingHours(new BigDecimal("8.0"));
        attendance.setOvertimeHours(BigDecimal.ZERO);
        attendance.setAutoCheckout(false);

        assertEquals(CheckinStatus.LATE, attendance.getCheckinStatus());
        assertEquals(CheckoutStatus.EARLY_OUT, attendance.getCheckoutStatus());
    }

    @Test
    public void testAbsentStatus() {
        Attendance attendance = new Attendance();
        attendance.setDate(LocalDate.now());
        attendance.setCheckinStatus(CheckinStatus.ABSENT);
        attendance.setCheckoutStatus(CheckoutStatus.ABSENT);
        attendance.setStandardWorkingHours(BigDecimal.ZERO);
        attendance.setOvertimeHours(BigDecimal.ZERO);
        attendance.setAutoCheckout(true);

        assertEquals(CheckinStatus.ABSENT, attendance.getCheckinStatus());
        assertEquals(CheckoutStatus.ABSENT, attendance.getCheckoutStatus());
        assertNull(attendance.getCheckIn());
        assertNull(attendance.getCheckOut());
    }

    @Test
    public void testApprovedLeaveStatus() {
        Attendance attendance = new Attendance();
        attendance.setDate(LocalDate.now());
        attendance.setCheckinStatus(CheckinStatus.APPROVED_LEAVE);
        attendance.setCheckoutStatus(CheckoutStatus.APPROVED_LEAVE);
        attendance.setStandardWorkingHours(BigDecimal.ZERO);
        attendance.setOvertimeHours(BigDecimal.ZERO);
        attendance.setAutoCheckout(true);

        assertEquals(CheckinStatus.APPROVED_LEAVE, attendance.getCheckinStatus());
        assertEquals(CheckoutStatus.APPROVED_LEAVE, attendance.getCheckoutStatus());
    }
}
