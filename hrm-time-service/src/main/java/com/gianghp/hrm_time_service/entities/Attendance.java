package com.gianghp.hrm_time_service.entities;

import com.gianghp.hrm.constants.NumericConstants;
import com.gianghp.hrm.entities.BaseEntity;
import com.gianghp.hrm.enums.CheckinStatus;
import com.gianghp.hrm.enums.CheckoutStatus;
import com.gianghp.hrm.enums.WorkType;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.UUID;

@Entity
@Table(name = "attendance")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Attendance extends BaseEntity {

    @ManyToOne
    @JoinColumn(name = "employee_id", nullable = false)
    private EmployeeCache employeeCache;

    @Column(name = "date", nullable = false)
    private LocalDate date;

    @Column(name = "check_in")
    private LocalTime checkIn;

    @Column(name = "check_out")
    private LocalTime checkOut;

    @Enumerated(EnumType.STRING)
    @Column(name = "checkin_status", length = 20)
    private CheckinStatus checkinStatus;

    @Enumerated(EnumType.STRING)
    @Column(name = "checkout_status", length = 20)
    private CheckoutStatus checkoutStatus;

    @Column(name = "standard_hours", precision = NumericConstants.DURATION_PRECISION, scale = NumericConstants.DURATION_SCALE)
    private BigDecimal standardWorkingHours;

    @Column(name = "overtime_hours", precision = NumericConstants.DURATION_PRECISION, scale = NumericConstants.DURATION_SCALE)
    private BigDecimal overtimeHours;

    @Column(name = "is_auto_checkout")
    private Boolean autoCheckout;

    @Column(name = "total_late_deduction_rate", precision = NumericConstants.DURATION_PRECISION, scale = NumericConstants.DURATION_SCALE)
    private BigDecimal totalLateDeductionRate;

    @Column(name = "total_early_out_deduction_rate", precision = NumericConstants.DURATION_PRECISION, scale = NumericConstants.DURATION_SCALE)
    private BigDecimal totalEarlyOutDeductionRate;

}
