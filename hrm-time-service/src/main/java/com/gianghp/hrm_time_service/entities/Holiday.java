package com.gianghp.hrm_time_service.entities;

import com.gianghp.hrm.entities.BaseEntity;
import com.gianghp.hrm.enums.HolidayType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.*;

import java.time.LocalDate;

@Entity
@Table(name = "holiday")
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class Holiday extends BaseEntity {

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "date", nullable = false)
    private LocalDate date;

    @Column(name = "holiday_type", nullable = false)
    private HolidayType holidayType;

    @Column(name = "description")
    private String description;

    @Column(name = "is_recurring")
    private boolean isRecurring = true;

}
