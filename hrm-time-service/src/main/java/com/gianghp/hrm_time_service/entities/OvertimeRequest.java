package com.gianghp.hrm_time_service.entities;

import com.gianghp.hrm.constants.NumericConstants;
import com.gianghp.hrm.entities.BaseEntity;
import com.gianghp.hrm.enums.ApproveStatus;
import jakarta.persistence.*;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.UUID;

@Entity
@Table(name = "overtime")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class OvertimeRequest extends BaseEntity {

    @ManyToOne
    @JoinColumn(name = "employee_id", nullable = false)
    private EmployeeCache employeeCache;

    @Column(name = "date", nullable = false)
    private LocalDate date;

    @Column(name = "reason")
    private String reason;

    @Column(name = "total_hours", precision = NumericConstants.DURATION_PRECISION, scale = NumericConstants.DURATION_SCALE)
    private BigDecimal totalHours;

    @ManyToOne
    @JoinColumn(name = "approver_id")
    private EmployeeCache approver;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", length = 20)
    private ApproveStatus status;

    @ManyToOne
    @JoinColumn(name = "overtime_setting_id")
    private OvertimeSetting overtimeSetting;

}
