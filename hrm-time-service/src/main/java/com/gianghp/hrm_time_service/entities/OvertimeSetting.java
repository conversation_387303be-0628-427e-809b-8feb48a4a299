package com.gianghp.hrm_time_service.entities;

import com.gianghp.hrm.constants.NumericConstants;
import com.gianghp.hrm.entities.BaseEntity;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Table(name = "overtime_setting")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class OvertimeSetting extends BaseEntity {

    @Column(name = "weekday_rate", nullable = false, precision = NumericConstants.DURATION_PRECISION, scale = NumericConstants.DURATION_SCALE)
    private BigDecimal weekdayRate;

    @Column(name = "holiday_rate", nullable = false, precision = NumericConstants.DURATION_PRECISION, scale = NumericConstants.DURATION_SCALE)
    private BigDecimal holidayRate;

    @Column(name = "minimum_overtime_minutes")
    private int minimumOvertimeMinutes;

    @Column(name = "max_hours_per_day", precision = NumericConstants.DURATION_PRECISION, scale = NumericConstants.DURATION_SCALE)
    private BigDecimal maxHoursPerDay;

    @Column(name = "max_hours_per_month")
    private int maxHoursPerMonth;

    @Column(name = "max_day_in_the_past_to_request")
    private int maxDayInThePastToRequest;

    @Column(name = "max_day_in_the_future_to_request")
    private int maxDayInTheFutureToRequest;

    @Column(name = "effective_date", nullable = false)
    private LocalDate effectiveDate;
}
