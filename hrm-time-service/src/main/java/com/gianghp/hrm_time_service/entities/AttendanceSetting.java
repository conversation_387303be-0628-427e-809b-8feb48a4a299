package com.gianghp.hrm_time_service.entities;

import com.gianghp.hrm.constants.NumericConstants;
import com.gianghp.hrm.entities.BaseEntity;
import com.gianghp.hrm.enums.WorkType;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

@Entity
@Table(name = "attendance_setting")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AttendanceSetting extends BaseEntity {

    // Giờ làm chuẩn
    @Column(name = "standard_check_in", nullable = false)
    private LocalTime standardCheckIn; // ví dụ: 08:00

    @Column(name = "standard_check_out", nullable = false)
    private LocalTime standardCheckOut; // ví dụ: 17:30

    // Khoảng cho phép trễ/về sớm
    @Column(name = "late_threshold_minutes", nullable = false)
    private int lateThresholdMinutes; // ví dụ: 10 → trễ sau 08:10

    @Column(name = "early_leave_threshold_minutes", nullable = false)
    private int earlyLeaveThresholdMinutes; // ví dụ: 10 → về sớm trước 17:20

    // Giờ làm tối thiểu trong ngày để tính là đi làm
    @Column(name = "min_working_minutes", nullable = false)
    private int minWorkingMinutes; // ví dụ: 240 phút (4 giờ)

    // Tổng số giờ cần để tính 1 ngày công
    @Column(name = "standard_working_hours", nullable = false, precision = NumericConstants.DURATION_PRECISION, scale = NumericConstants.DURATION_SCALE)
    private BigDecimal standardWorkingHours; // ví dụ: 480 phút (8 giờ)

    // Thêm vào cả Entity và DTO
    @Column(name = "check_in_start", nullable = false)
    private LocalTime checkInStart;
    @Column(name = "check_in_end", nullable = false)
    private LocalTime checkInEnd;     // ví dụ: 09:00 → không cho phép checkin sau thời gian này

    @Column(name = "check_out_start", nullable = false)
    private LocalTime checkOutStart;  // ví dụ: 16:00 → bắt đầu cho phép checkout
    @Column(name = "check_out_end", nullable = false)
    private LocalTime checkOutEnd;    // ví dụ: 20:00 → không cho phép checkout sau thời gian này


    @Enumerated(EnumType.STRING)
    @Column(name = "work_type", nullable = false, length = 20)
    private WorkType workType;

    @Column(name = "effective_date", nullable = false)
    private LocalDate effectiveDate;

    @Column(name = "late_deduction_rate", nullable = false, precision = NumericConstants.DURATION_PRECISION, scale = NumericConstants.DURATION_SCALE)
    private BigDecimal lateDeductionRate;

    @Column(name = "early_out_deduction_rate", nullable = false, precision = NumericConstants.DURATION_PRECISION, scale = NumericConstants.DURATION_SCALE)
    private BigDecimal earlyOutDeductionRate;

    @Column(name = "minute_per_deduction", nullable = false)
    private int minutePerDeduction;

}
