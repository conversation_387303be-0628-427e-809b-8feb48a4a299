package com.gianghp.hrm_time_service.entities;

import com.gianghp.hrm.entities.BaseEntity;
import com.gianghp.hrm.enums.LeaveRequestStatus;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDate;
import java.util.UUID;

@Entity
@Table(name = "leave_request")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class LeaveRequest extends BaseEntity {
    @ManyToOne
    @JoinColumn(name = "employee_id", nullable = false)
    private EmployeeCache employeeCache;

    @ManyToOne
    @JoinColumn(name = "leave_type_id", nullable = false)
    private LeaveType leaveType;

    @Column(name = "start_date", nullable = false)
    private LocalDate startDate;

    @Column(name = "end_date", nullable = false)
    private LocalDate endDate;

    @Column(name = "reason")
    private String reason;

    @Column(name = "number_of_days", nullable = false)
    private int numberOfDays;

    @Enumerated(EnumType.STRING)
    @Column(length = 20, nullable = false)
    private LeaveRequestStatus status = LeaveRequestStatus.PENDING;

    @ManyToOne
    @JoinColumn(name = "approver_id")
    private EmployeeCache approver;

    @Column(name = "approved_at")
    private LocalDate approvedAt;

    @Column(name = "note")
    private String note;
}

