package com.gianghp.hrm_time_service.entities;

import com.gianghp.hrm.enums.WorkType;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Table(name = "employee_cache")
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EmployeeCache {
    @Id
    @Column(name = "id")
    private UUID id;

    @Column(name = "user_id", nullable = false, unique = true)
    private UUID userId;

    @Column(name = "department_id")
    private UUID departmentId;

    @Column(name = "employee_code")
    private String employeeCode;

    @Column(name = "full_name")
    private String fullName;

    @Column(name = "department_name")
    private String departmentName;

    @Column(name = "date_of_joining")
    private LocalDate dateOfJoining;

    @Column(name = "designation_name")
    private String designationName;

    @Column(name = "work_type", nullable = false, length = 20)
    @Enumerated(EnumType.STRING)
    private WorkType workType;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @PreUpdate
    @PrePersist
    public void updateTimestamp() {
        this.updatedAt = LocalDateTime.now();
    }
}
