package com.gianghp.hrm_time_service.entities;

import com.gianghp.hrm.entities.BaseEntity;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.SuperBuilder;

@Entity
@Table(name = "leave_type")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class LeaveType extends BaseEntity {
    private String name;

    private boolean isActive;
}
