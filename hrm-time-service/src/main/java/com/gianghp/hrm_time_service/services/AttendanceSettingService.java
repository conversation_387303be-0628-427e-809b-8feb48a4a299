package com.gianghp.hrm_time_service.services;

import com.gianghp.hrm.enums.WorkType;
import com.gianghp.hrm_time_service.dtos.post.AttendanceSettingCreateDto;
import com.gianghp.hrm_time_service.dtos.get.AttendanceSettingDto;
import com.gianghp.hrm_time_service.entities.AttendanceSetting;
import com.gianghp.hrm_time_service.mappers.AttendanceSettingMapper;
import com.gianghp.hrm_time_service.producers.KafkaTimeProducer;
import com.gianghp.hrm_time_service.repositories.AttendanceSettingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
@RequiredArgsConstructor
public class AttendanceSettingService {

    private final AttendanceSettingRepository attendanceSettingRepository;
    private final AttendanceSettingMapper attendanceSettingMapper;
    private final KafkaTimeProducer kafkaTimeProducer;

    public List<AttendanceSettingDto> findAll() {
        return attendanceSettingRepository.findAll().stream()
                .map(attendanceSettingMapper::toDto)
                .toList();
    }


    public AttendanceSettingDto findCurrentSetting(WorkType workType) {
        AttendanceSetting setting = attendanceSettingRepository.findTopByWorkTypeAndEffectiveDateLessThanEqualOrderByEffectiveDateDesc(workType, LocalDate.now());
        return attendanceSettingMapper.toDto(setting);
    }


    public AttendanceSettingDto create(AttendanceSettingCreateDto createDto) {
        AttendanceSetting setting = attendanceSettingMapper.toEntity(createDto);
        attendanceSettingRepository.save(setting);
        kafkaTimeProducer.sendWhenCreateNewAttendanceSetting(attendanceSettingMapper.toBasicDto(setting));
        return attendanceSettingMapper.toDto(setting);
    }
}
