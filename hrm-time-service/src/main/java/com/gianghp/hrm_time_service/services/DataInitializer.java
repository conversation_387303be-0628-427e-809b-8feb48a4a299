package com.gianghp.hrm_time_service.services;

import com.gianghp.hrm.enums.*;
import com.gianghp.hrm_time_service.entities.*;
import com.gianghp.hrm_time_service.repositories.*;
import java.util.ArrayList;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class DataInitializer {

    private final LeaveTypeRepository leaveTypeRepository;
    private final AttendanceSettingRepository attendanceSettingRepository;
    private final OvertimeSettingRepository overtimeSettingRepository;
    private final EmployeeCacheRepository employeeCacheRepository;
    private final AttendanceRepository attendanceRepository;
    private final LeaveRequestRepository leaveRequestRepository;
    private final OvertimeRequestRepository overtimeRequestRepository;
    private final HolidayRepository holidayRepository;

    public void init() {
        log.info("Starting Time Service data initialization...");

        initSampleAttendance();
        initSampleLeaveRequests();
//        initSampleOvertimeRequests();
        initSampleHolidays();

        log.info("Time Service data initialization completed successfully!");
    }

    public void initSampleAttendance() {
        if (attendanceRepository.count() > 0) return;

        log.info("Initializing attendance records for testing auto-checkout and working hours calculation...");

        List<EmployeeCache> employees = employeeCacheRepository.findAll().stream().limit(4).toList();
        if (employees.isEmpty()) {
            log.warn("No employee found for attendance test data");
            return;
        }

        LocalDate today = LocalDate.now();
        List<Attendance> attendanceList = new ArrayList<>();

        for (int i = 0; i < employees.size(); i++) {
            EmployeeCache emp = employees.get(i);

            Attendance attendance = new Attendance();
            attendance.setEmployeeCache(emp);
            attendance.setDate(today);
            attendance.setAutoCheckout(false);

            switch (i) {
                case 0:
                    // 1. Đầy đủ check-in, chưa check-out → sẽ được auto-checkout
                    attendance.setCheckIn(LocalTime.of(8, 0));
                    attendance.setCheckOut(null);
                    attendance.setCheckinStatus(CheckinStatus.ON_TIME);
                    attendance.setCheckoutStatus(CheckoutStatus.NOT_CHECKED_OUT);
                    break;

                case 1:
                    // 2. Đã check-in sớm và check-out muộn (sinh overtime)
                    attendance.setCheckIn(LocalTime.of(7, 30));
                    attendance.setCheckOut(LocalTime.of(18, 30));
                    attendance.setCheckinStatus(CheckinStatus.ON_TIME);
                    attendance.setCheckoutStatus(CheckoutStatus.ON_TIME);

                    // Tạo OvertimeRequest
                    OvertimeRequest ot = new OvertimeRequest();
                    ot.setEmployeeCache(emp);
                    ot.setDate(today);
                    ot.setStatus(ApproveStatus.APPROVED);
                    ot.setTotalHours(new BigDecimal("2.0"));
                    overtimeRequestRepository.save(ot);
                    break;

                case 2:
                    // 3. Check-in trễ và check-out sớm
                    attendance.setCheckIn(LocalTime.of(8, 30));
                    attendance.setCheckOut(LocalTime.of(17, 0));
                    attendance.setCheckinStatus(CheckinStatus.LATE);
                    attendance.setCheckoutStatus(CheckoutStatus.EARLY_OUT);
                    break;

                case 3:
                    // 4. Được approved leave
                    attendance.setCheckIn(null);
                    attendance.setCheckOut(null);
                    attendance.setCheckinStatus(CheckinStatus.APPROVED_LEAVE);
                    attendance.setCheckoutStatus(CheckoutStatus.APPROVED_LEAVE);
                    break;
            }

            attendanceList.add(attendance);
        }

        attendanceRepository.saveAll(attendanceList);
        log.info("Test attendance records initialized.");
    }




    private void initSampleLeaveRequests() {
        if (leaveRequestRepository.count() > 0) return;

        log.info("Initializing sample leave requests for one employee...");
        List<EmployeeCache> employees = employeeCacheRepository.findAll();
        if (employees.size() < 2) return;

        EmployeeCache emp1 = employees.get(0);
        EmployeeCache approver = employees.get(1);
        LeaveType annualLeave = leaveTypeRepository.findAll().stream().filter(lt -> lt.getName().equalsIgnoreCase("Annual Leave")).findFirst().orElse(null);
        LeaveType sickLeave = leaveTypeRepository.findAll().stream().filter(lt -> lt.getName().equalsIgnoreCase("Sick Leave")).findFirst().orElse(null);

        if (emp1 != null && approver != null && annualLeave != null && sickLeave != null) {
            List<LeaveRequest> leaves = List.of(
                    new LeaveRequest(emp1, annualLeave, LocalDate.now().plusDays(3), LocalDate.now().plusDays(4), "Vacation", 2, LeaveRequestStatus.PENDING, approver, null, "Awaiting approval"),
                    new LeaveRequest(emp1, sickLeave, LocalDate.now().minusDays(2), LocalDate.now().minusDays(1), "Flu", 2, LeaveRequestStatus.APPROVED, approver, LocalDate.now().minusDays(1), "Take care"),
                    new LeaveRequest(emp1, annualLeave, LocalDate.now().plusWeeks(1), LocalDate.now().plusWeeks(1).plusDays(2), "Travel", 3, LeaveRequestStatus.PENDING, approver, null, "Planned leave")
            );
            leaveRequestRepository.saveAll(leaves);
            log.info("Created {} leave requests for {}", leaves.size(), emp1.getFullName());
        }
    }

//    private void initSampleOvertimeRequests() {
//        if (overtimeRequestRepository.count() > 0) return;
//
//        log.info("Initializing sample overtime requests for one employee...");
//        List<EmployeeCache> employees = employeeCacheRepository.findAll();
//        if (employees.size() < 2) return;
//
//        EmployeeCache emp1 = employees.get(0);
//        EmployeeCache approver = employees.get(1);
//        OvertimeSetting setting = overtimeSettingRepository.findAll().stream().findFirst().orElse(null);
//
//        if (emp1 != null && approver != null && setting != null) {
//            OvertimeRequest ot1 = new OvertimeRequest();
//            ot1.setEmployeeCache(emp1);
//            ot1.setDate(LocalDate.now().minusDays(2));
//            ot1.setTotalHours(BigDecimal.valueOf(2.0)); // 18:00 - 20:00 = 2 hours
//            ot1.setReason("Release patch");
//            ot1.setApprover(approver);
//            ot1.setStatus(ApproveStatus.PENDING);
//            ot1.setOvertimeSetting(setting);
//
//            OvertimeRequest ot2 = new OvertimeRequest();
//            ot2.setEmployeeCache(emp1);
//            ot2.setDate(LocalDate.now().minusDays(3));
//            ot2.setTotalHours(BigDecimal.valueOf(2.0)); // 19:00 - 21:00 = 2 hours
//            ot2.setReason("System migration");
//            ot2.setApprover(approver);
//            ot2.setStatus(ApproveStatus.APPROVED);
//            ot2.setOvertimeSetting(setting);
//
//            OvertimeRequest ot3 = new OvertimeRequest();
//            ot3.setEmployeeCache(emp1);
//            ot3.setDate(LocalDate.now().minusDays(5));
//            ot3.setTotalHours(BigDecimal.valueOf(2.0)); // 17:30 - 19:30 = 2 hours
//            ot3.setReason("Client issue fix");
//            ot3.setApprover(approver);
//            ot3.setStatus(ApproveStatus.REJECTED);
//            ot3.setOvertimeSetting(setting);
//
//            overtimeRequestRepository.saveAll(List.of(ot1, ot2, ot3));
//            log.info("Created 3 sample overtime requests for {}", emp1.getFullName());
//        }
//    }

    private void initSampleHolidays() {
        if (holidayRepository.count() > 0) return;

        log.info("Initializing sample holidays...");
        List<Holiday> holidays = List.of(
                new Holiday("New Year's Day", LocalDate.of(2024, 1, 1), HolidayType.PUBLIC, "New Year's Day", true),
                new Holiday("Independence Day", LocalDate.of(2024, 7, 4), HolidayType.PUBLIC, "Independence Day", true),
                new Holiday("Thanksgiving Day", LocalDate.of(2024, 11, 28), HolidayType.PUBLIC, "Thanksgiving Day", true),
                new Holiday("Christmas Day", LocalDate.of(2027, 12, 25), HolidayType.PUBLIC, "Christmas Day", false)
        );
        holidayRepository.saveAll(holidays);
        log.info("Created {} sample holidays", holidays.size());
    }
}
