package com.gianghp.hrm_time_service.services;

import com.gianghp.hrm_time_service.dtos.post.HolidayCreateDto;
import com.gianghp.hrm_time_service.dtos.get.HolidayDto;
import com.gianghp.hrm_time_service.mappers.HolidayMapper;
import com.gianghp.hrm_time_service.repositories.HolidayRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class HolidayService {
    private final HolidayRepository holidayRepository;
    private final HolidayMapper holidayMapper;

    public List<HolidayDto> getAll() {
        return holidayRepository.findAll().stream()
                .map(holidayMapper::toDto)
                .toList();
    }

    public HolidayDto createHoliday(HolidayCreateDto holidayCreateDto) {
        return holidayMapper.toDto(holidayRepository.save(holidayMapper.toEntity(holidayCreateDto)));
    }
}
