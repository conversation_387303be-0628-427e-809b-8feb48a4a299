package com.gianghp.hrm_time_service.services;

import com.gianghp.hrm_time_service.dtos.get.EmployeeDashboardTimeDto;
import com.gianghp.hrm_time_service.dtos.get.WorkingSummaryThisMonthDto;
import com.gianghp.hrm_time_service.entities.EmployeeCache;
import com.gianghp.hrm_time_service.repositories.EmployeeCacheRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class DashBoardService {
    private final AttendanceService attendanceService;
    private final OvertimeRequestService overtimeRequestService;
    private final LeaveRequestService leaveRequestService;
    private final EmployeeCacheRepository employeeCacheRepository;

    public EmployeeDashboardTimeDto getEmployeeDashBoardInfo(UUID employeeId) {
        EmployeeDashboardTimeDto employeeDashboardTimeDto = new EmployeeDashboardTimeDto();
        employeeDashboardTimeDto.setEmployeeId(employeeId);
        employeeDashboardTimeDto.setTotalWorkingHoursThisMonth(attendanceService.calculateTotalWorkingHoursThisMonth(employeeId));
        employeeDashboardTimeDto.setTotalWorkingHoursThisWeek(attendanceService.calculateTotalWorkingHoursThisWeek(employeeId));
        employeeDashboardTimeDto.setTotalOvertimeHoursThisMonth(overtimeRequestService.calculateOvertimeHoursThisMonth(employeeId));
        employeeDashboardTimeDto.setAttendanceToday(attendanceService.getAttendanceToday(employeeId));
        employeeDashboardTimeDto.setAttendanceThisYears(attendanceService.getAttendanceByYear(employeeId, LocalDate.now().getYear()));
        employeeDashboardTimeDto.setLeaveThisMonth(leaveRequestService.getLeaveThisMonth(employeeId));
        employeeDashboardTimeDto.setOvertimeThisMonth(overtimeRequestService.getOvertimeThisMonth(employeeId));
        return employeeDashboardTimeDto;
    }

    public WorkingSummaryThisMonthDto getWorkingSummaryThisMonth(UUID employeeId) {
        WorkingSummaryThisMonthDto workingSummaryThisMonthDto = new WorkingSummaryThisMonthDto();
        workingSummaryThisMonthDto.setTotalHoursToday(attendanceService.calculateTotalWorkingHoursToday(employeeId));
        workingSummaryThisMonthDto.setTotalHoursThisMonth(attendanceService.calculateTotalWorkingHoursThisMonth(employeeId));
        workingSummaryThisMonthDto.setOvertimeHoursThisMonth(overtimeRequestService.calculateOvertimeHoursThisMonth(employeeId));
        workingSummaryThisMonthDto.setTotalHoursThisWeek(attendanceService.calculateTotalWorkingHoursThisWeek(employeeId));
        return workingSummaryThisMonthDto;
    }


    public EmployeeDashboardTimeDto getMyDashBoardInfo(UUID userId) {
        EmployeeCache employee = employeeCacheRepository.findByUserId(userId).orElseThrow(() -> new RuntimeException("Employee not found"));
        return getEmployeeDashBoardInfo(employee.getId());
    }

    public WorkingSummaryThisMonthDto getMyWorkingSummaryThisMonth(UUID userId) {
        EmployeeCache employee = employeeCacheRepository.findByUserId(userId).orElseThrow(() -> new RuntimeException("Employee not found"));
        return getWorkingSummaryThisMonth(employee.getId());
    }
}
