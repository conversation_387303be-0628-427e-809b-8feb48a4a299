package com.gianghp.hrm_time_service.services;

import com.gianghp.hrm.dtos.AttendanceBasicDto;
import com.gianghp.hrm.dtos.AttendanceBasicWrapperDto;
import com.gianghp.hrm.enums.ApproveStatus;
import com.gianghp.hrm.enums.CheckinStatus;
import com.gianghp.hrm.enums.CheckoutStatus;
import com.gianghp.hrm.enums.DayType;
import com.gianghp.hrm.enums.LeaveRequestStatus;
import com.gianghp.hrm.enums.WorkType;
import com.gianghp.hrm.utils.DateTimeUtils;
import com.gianghp.hrm_time_service.dtos.get.AttendanceDto;
import com.gianghp.hrm_time_service.dtos.get.AttendanceMonthDto;
import com.gianghp.hrm_time_service.dtos.params.AdminAttendanceRequestParams;
import com.gianghp.hrm_time_service.dtos.params.ManagerAttendanceRequestParams;
import com.gianghp.hrm_time_service.dtos.params.MyAttendanceRequestParams;
import com.gianghp.hrm_time_service.dtos.get.AttendanceTodayDto;
import com.gianghp.hrm_time_service.dtos.put.AttendanceUpdateDto;
import com.gianghp.hrm_time_service.entities.Attendance;
import com.gianghp.hrm_time_service.entities.AttendanceSetting;
import com.gianghp.hrm_time_service.entities.EmployeeCache;
import com.gianghp.hrm_time_service.entities.Holiday;
import com.gianghp.hrm_time_service.entities.LeaveRequest;
import com.gianghp.hrm_time_service.entities.OvertimeRequest;
import com.gianghp.hrm_time_service.entities.OvertimeSetting;
import com.gianghp.hrm_time_service.mappers.AdminAttendanceRequestParamsMapper;
import com.gianghp.hrm_time_service.mappers.AttendanceMapper;
import com.gianghp.hrm_time_service.producers.KafkaTimeProducer;
import com.gianghp.hrm_time_service.repositories.AttendanceRepository;
import com.gianghp.hrm_time_service.repositories.AttendanceSettingRepository;
import com.gianghp.hrm_time_service.repositories.EmployeeCacheRepository;
import com.gianghp.hrm_time_service.repositories.HolidayRepository;
import com.gianghp.hrm_time_service.repositories.LeaveRequestRepository;
import com.gianghp.hrm_time_service.repositories.OvertimeRequestRepository;
import com.gianghp.hrm_time_service.repositories.OvertimeSettingRepository;
import com.gianghp.hrm_time_service.specifications.AttendanceSpecification;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class AttendanceService {

  private final AttendanceRepository attendanceRepository;
  private final AttendanceMapper attendanceMapper;
  private final EmployeeCacheRepository employeeCacheRepository;
  private final AttendanceSettingRepository attendanceSettingRepository;
  private final AdminAttendanceRequestParamsMapper adminAttendanceRequestParamsMapper;
  private final OvertimeSettingRepository overtimeSettingRepository;
  private final HolidayRepository holidayRepository;
  private final OvertimeRequestRepository overtimeRequestRepository;
  private final LeaveRequestRepository leaveRequestRepository;
  private final KafkaTimeProducer kafkaTimeProducer;

  public Page<AttendanceDto> findAll(Pageable pageable) {
    return attendanceRepository.findAll(pageable).map(attendanceMapper::toDto);
  }


  public AttendanceDto findById(UUID id) {
    Attendance attendance = attendanceRepository.findById(id)
        .orElseThrow(() -> new RuntimeException("Attendance not found"));
    return attendanceMapper.toDto(attendance);
  }

  public Attendance getAttendancesToday(UUID employeeId) {
    LocalDate today = LocalDate.now();

    return attendanceRepository.findFirstByEmployeeCache_IdAndDate(employeeId, today);
  }

  public Map<Month, AttendanceMonthDto> getAttendanceByYear(UUID employeeId, int year) {
    LocalDate start = Year.of(year).atDay(1); // 01/01/yyyy
    LocalDate end = Year.of(year).atMonth(12).atEndOfMonth(); // 31/12/yyyy

    List<Attendance> attendances = attendanceRepository.findByEmployeeCache_IdAndDateBetween(
        employeeId, start, end
    );

    // Group theo tháng
    Map<Month, List<Attendance>> grouped = attendances.stream()
        .collect(Collectors.groupingBy(att -> att.getDate().getMonth(), TreeMap::new,
            Collectors.toList()));

    // Tính tổng hợp AttendanceMonthDto
    Map<Month, AttendanceMonthDto> result = new TreeMap<>();
    for (Month month : Month.values()) {
      List<Attendance> list = grouped.getOrDefault(month, Collections.emptyList());

      int total = list.size();
      int onTime = (int) list.stream()
          .filter(att -> att.getCheckinStatus() == CheckinStatus.ON_TIME).count();
      int late = (int) list.stream().filter(att -> att.getCheckinStatus() == CheckinStatus.LATE)
          .count();
      int earlyOut = (int) list.stream().filter(
          att -> att.getCheckoutStatus() == CheckoutStatus.EARLY_OUT
              || att.getCheckoutStatus() == CheckoutStatus.FORGOT_CHECK_OUT).count();
      int absent = (int) list.stream().filter(att -> att.getCheckinStatus() == CheckinStatus.ABSENT
          || att.getCheckinStatus() == CheckinStatus.UNEXPLAINED_ABSENCE
          || att.getCheckinStatus() == CheckinStatus.APPROVED_LEAVE).count();

      result.put(month, new AttendanceMonthDto(total, onTime, late, earlyOut, absent));
    }

    return result;
  }

  public AttendanceTodayDto getAttendanceToday(UUID employeeId) {
    Attendance attendance = getAttendancesToday(employeeId);
    if (attendance == null) {
      return new AttendanceTodayDto(false, false, null, null, null, null);
    }
    return new AttendanceTodayDto(
        attendance.getCheckIn() != null || attendance.getCheckinStatus() != null,
        attendance.getCheckOut() != null || attendance.getCheckoutStatus() != null,
        attendance.getCheckIn(),
        attendance.getCheckOut(),
        attendance.getCheckinStatus(),
        attendance.getCheckoutStatus()
    );
  }


  public Page<AttendanceDto> findByParams(AdminAttendanceRequestParams params) {
    Pageable pageable = PageRequest.of(
        params.getPage(),
        params.getSize(),
        params.getSortDir().equalsIgnoreCase("desc")
            ? Sort.by(params.getSortBy()).descending()
            : Sort.by(params.getSortBy()).ascending()
    );

    // Handle month and year parameters

    if (params.getMonth() != null && !params.getMonth().isBlank()) {
      YearMonth ym = YearMonth.parse(params.getMonth());
      params.setStartDate(ym.atDay(1));
      params.setEndDate(ym.atEndOfMonth());
    }

    // Use JPA Specifications for filtering
    Specification<Attendance> spec = AttendanceSpecification.buildSpecification(params);
    return attendanceRepository.findAll(spec, pageable).map(attendanceMapper::toDto);
  }


  public void createAndCheckIn(UUID employeeId) {
    if (attendanceRepository.existsByEmployeeCache_IdAndDate(employeeId, LocalDate.now())) {
      throw new RuntimeException("Attendance already exists for today");
    }

    LocalDate today = LocalDate.now();
    LocalTime now = LocalTime.now();

    EmployeeCache employeeCache = employeeCacheRepository.findById(employeeId)
        .orElseThrow(() -> new RuntimeException("Employee not found"));

    AttendanceSetting attendanceSetting = attendanceSettingRepository
        .findTopByWorkTypeAndEffectiveDateLessThanEqualOrderByEffectiveDateDesc(
            employeeCache.getWorkType(), today
        );

    // Kiểm tra ngoài khung thời gian cho phép check-in
    if (now.isBefore(attendanceSetting.getCheckInStart()) || now.isAfter(
        attendanceSetting.getCheckInEnd())) {
      throw new RuntimeException("Check-in time is not allowed at this moment.");
    }

    Attendance attendance = new Attendance();
    attendance.setDate(today);
    attendance.setEmployeeCache(employeeCache);
    attendance.setCheckIn(now);

    // Tính mốc thời gian trễ cho phép
    LocalTime lateThresholdTime = attendanceSetting.getStandardCheckIn()
        .plusMinutes(attendanceSetting.getLateThresholdMinutes());

    CheckinStatus checkinStatus;
    if (now.isAfter(lateThresholdTime)) {
      checkinStatus = CheckinStatus.LATE;
    } else {
      checkinStatus = CheckinStatus.ON_TIME;
    }

    attendance.setCheckinStatus(checkinStatus);
    attendance.setCheckoutStatus(CheckoutStatus.NOT_CHECKED_OUT);
    attendanceRepository.save(attendance);
  }


  public void update(UUID id, AttendanceUpdateDto updateDto) {
    Attendance attendance = attendanceRepository.findById(id)
        .orElseThrow(() -> new RuntimeException("Attendance not found"));
    attendanceMapper.updateEntity(updateDto, attendance);
    attendanceRepository.save(attendance);
  }

  public List<Attendance> getAttendancesThisWeek(UUID employeeId) {
    LocalDate today = LocalDate.now();

    LocalDate startOfWeek = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
    LocalDate endOfWeek = today.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));

    return attendanceRepository.findByEmployeeCache_IdAndDateBetween(employeeId, startOfWeek,
        endOfWeek);
  }

  public List<Attendance> getAttendancesThisMonth(UUID employeeId) {
    LocalDate today = LocalDate.now();

    LocalDate startOfMonth = today.withDayOfMonth(1);
    LocalDate endOfMonth = today.with(TemporalAdjusters.lastDayOfMonth());

    return attendanceRepository.findByEmployeeCache_IdAndDateBetween(employeeId, startOfMonth,
        endOfMonth);
  }

  public BigDecimal calculateTotalWorkingHoursThisWeek(UUID employeeId) {
    log.info("Calculating total working hours this week for employee {}", employeeId);
    return getAttendancesThisWeek(employeeId).stream()
        .map(attendance -> {
          BigDecimal standardHours =
              attendance.getStandardWorkingHours() != null ? attendance.getStandardWorkingHours()
                  : BigDecimal.ZERO;
          BigDecimal overtimeHours =
              attendance.getOvertimeHours() != null ? attendance.getOvertimeHours()
                  : BigDecimal.ZERO;
          return standardHours.add(overtimeHours);
        })
        .reduce(BigDecimal.ZERO, BigDecimal::add);
  }

  public BigDecimal calculateTotalWorkingHoursThisMonth(UUID employeeId) {
    return getAttendancesThisMonth(employeeId).stream()
        .map(attendance -> {
          BigDecimal standardHours =
              attendance.getStandardWorkingHours() != null ? attendance.getStandardWorkingHours()
                  : BigDecimal.ZERO;
          BigDecimal overtimeHours =
              attendance.getOvertimeHours() != null ? attendance.getOvertimeHours()
                  : BigDecimal.ZERO;
          return standardHours.add(overtimeHours);
        })
        .reduce(BigDecimal.ZERO, BigDecimal::add);
  }

  public BigDecimal calculateTotalWorkingHoursToday(UUID employeeId) {
    Attendance attendance = getAttendancesToday(employeeId);
    if (attendance == null) {
      return BigDecimal.ZERO;
    }

    BigDecimal standardHours =
        attendance.getStandardWorkingHours() != null ? attendance.getStandardWorkingHours()
            : BigDecimal.ZERO;
    BigDecimal overtimeHours =
        attendance.getOvertimeHours() != null ? attendance.getOvertimeHours() : BigDecimal.ZERO;
    BigDecimal totalHours = standardHours.add(overtimeHours);

    if (totalHours.compareTo(BigDecimal.ZERO) > 0) {
      return totalHours;
    }

    // Nếu chưa có giờ làm được tính, tính từ check-in đến hiện tại
    LocalTime startTime = attendance.getCheckIn();
    return DateTimeUtils.calculateHoursBetween(startTime, LocalTime.now());
  }


  public Page<AttendanceDto> findMyByParams(UUID userId, MyAttendanceRequestParams params) {
    EmployeeCache employee = employeeCacheRepository.findByUserId(userId)
        .orElseThrow(() -> new RuntimeException("Employee not found"));
    AdminAttendanceRequestParams adminParams = adminAttendanceRequestParamsMapper.fromMyParams(
        params);
    adminParams.setEmployeeId(employee.getId());
    adminParams.setEmployeeCode(employee.getEmployeeCode());
    adminParams.setEmployeeName(employee.getFullName());
    adminParams.setDepartmentId(employee.getDepartmentId());
    return findByParams(adminParams);
  }

  public Page<AttendanceDto> findByManagerParams(UUID userId,
      ManagerAttendanceRequestParams params) {
    EmployeeCache employee = employeeCacheRepository.findByUserId(userId)
        .orElseThrow(() -> new RuntimeException("Employee not found"));
    AdminAttendanceRequestParams adminParams = adminAttendanceRequestParamsMapper.fromManagerParams(
        params);
    adminParams.setDepartmentId(employee.getDepartmentId());
    return findByParams(adminParams);
  }

  public void myCreateAndCheckIn(UUID userId) {
    EmployeeCache employee = employeeCacheRepository.findByUserId(userId)
        .orElseThrow(() -> new RuntimeException("Employee not found"));
    createAndCheckIn(employee.getId());
  }

  public void myCheckOut(UUID userId) {
    EmployeeCache employee = employeeCacheRepository.findByUserId(userId)
        .orElseThrow(() -> new RuntimeException("Employee not found"));

    Attendance attendance = getAttendancesToday(employee.getId());
    if (attendance == null) {
      throw new RuntimeException("No attendance found for today");
    }

    if (attendance.getCheckOut() != null) {
      throw new RuntimeException("Attendance already checked out");
    }

    LocalTime now = LocalTime.now();

    AttendanceSetting attendanceSetting = attendanceSettingRepository
        .findTopByWorkTypeAndEffectiveDateLessThanEqualOrderByEffectiveDateDesc(
            employee.getWorkType(), attendance.getDate()
        );

    if (attendanceSetting == null) {
      throw new RuntimeException(
          "Attendance setting not found for work type: " + employee.getWorkType());
    }

    LocalTime earlyCheckoutThreshold = attendanceSetting.getStandardCheckOut()
        .minusMinutes(attendanceSetting.getEarlyLeaveThresholdMinutes());

    CheckoutStatus checkoutStatus = now.isBefore(earlyCheckoutThreshold)
        ? CheckoutStatus.EARLY_OUT
        : CheckoutStatus.ON_TIME;

    attendance.setCheckOut(now);
    attendance.setCheckoutStatus(checkoutStatus);
    attendance.setAutoCheckout(false);

    attendanceRepository.save(attendance);
  }

  public void autoMarkAbsentAll() {
    List<EmployeeCache> allEmployees = employeeCacheRepository.findAll();
    LocalDate today = LocalDate.now();
    List<Attendance> toSaveOrUpdate = new ArrayList<>();
    List<Holiday> holidayList = holidayRepository.findByDateLessThanEqualOrderByDateDesc(today);
    List<LocalDate> holidays = holidayList.stream()
        .map(Holiday::getDate)
        .toList();

    DayType dayType = DateTimeUtils.getDayType(today, holidays);

    log.info("===> [START] Auto mark absent & leave for {} employees", allEmployees.size());

    for (EmployeeCache employee : allEmployees) {
      Attendance attendance = getAttendancesToday(employee.getId());

      if (dayType == DayType.WEEKEND) {
        log.info("Today is weekend, skipping");
        return;
      }
      if (dayType == DayType.HOLIDAY) {
        if (attendance == null) {
          attendance = new Attendance();
          attendance.setEmployeeCache(employee);
          attendance.setDate(today);
        }
        attendance.setCheckinStatus(CheckinStatus.HOLIDAY);
        attendance.setCheckoutStatus(CheckoutStatus.HOLIDAY);
        attendance.setCheckIn(null);
        attendance.setCheckOut(null);
        attendance.setAutoCheckout(true);
        toSaveOrUpdate.add(attendance);
        continue;
      }

      LeaveRequest leaveRequest = leaveRequestRepository
          .findByEmployeeCache_IdAndStartDateLessThanEqualAndEndDateGreaterThanEqualAndStatus(
              employee.getId(), today, today, LeaveRequestStatus.APPROVED
          ).orElse(null);

      if (leaveRequest != null) {

        if (attendance == null) {
          attendance = new Attendance();
          attendance.setEmployeeCache(employee);
          attendance.setDate(today);
        } else {
          log.info("Updating existing attendance to APPROVED_LEAVE");
        }

        attendance.setCheckinStatus(CheckinStatus.APPROVED_LEAVE);
        attendance.setCheckoutStatus(CheckoutStatus.APPROVED_LEAVE);
        attendance.setCheckIn(null);
        attendance.setCheckOut(null);
        attendance.setAutoCheckout(true);

        toSaveOrUpdate.add(attendance);
        continue;
      }

      if (attendance == null) {
        attendance = new Attendance();
        attendance.setEmployeeCache(employee);
        attendance.setDate(today);
        attendance.setCheckinStatus(CheckinStatus.UNEXPLAINED_ABSENCE);
        attendance.setCheckoutStatus(CheckoutStatus.ABSENT);
        attendance.setAutoCheckout(true);
        toSaveOrUpdate.add(attendance);
      } else if (attendance.getCheckIn() == null &&
          attendance.getCheckinStatus() != CheckinStatus.UNEXPLAINED_ABSENCE) {
        attendance.setCheckinStatus(CheckinStatus.UNEXPLAINED_ABSENCE);
        attendance.setCheckoutStatus(CheckoutStatus.ABSENT);
        attendance.setAutoCheckout(true);

        toSaveOrUpdate.add(attendance);
      }
    }

    if (!toSaveOrUpdate.isEmpty()) {
      log.info("Saving {} updated/created attendance records", toSaveOrUpdate.size());
      attendanceRepository.saveAll(toSaveOrUpdate);
    } else {
      log.info("No attendance updates needed");
    }

    log.info("===> [END] Auto mark absent & leave done");
  }


  public void autoCheckOutAllAndCalculateWorkingHours() {
    List<Holiday> holidayList = holidayRepository.findByDateLessThanEqualOrderByDateDesc(LocalDate.now());
    List<LocalDate> holidays = holidayList.stream()
        .map(Holiday::getDate)
        .toList();

    DayType dayType = DateTimeUtils.getDayType(LocalDate.now(), holidays);
    if (dayType == DayType.WEEKEND) {
      log.info("Today is weekend, skipping");
      return;
    }

    log.info("===> [START] Auto check out all and calculate working hours");
    List<EmployeeCache> allEmployees = employeeCacheRepository.findAll();
    List<Attendance> toUpdate = new ArrayList<>();
    LocalTime now = LocalTime.now();
    AttendanceSetting attendanceSetting = attendanceSettingRepository
        .findTopByWorkTypeAndEffectiveDateLessThanEqualOrderByEffectiveDateDesc(
            WorkType.FULL_TIME, LocalDate.now()
        );



    if (attendanceSetting == null) {
      throw new RuntimeException("Attendance setting not found for work type: " + WorkType.FULL_TIME);
    }


    for (EmployeeCache employee : allEmployees) {
      Attendance attendance = getAttendancesToday(employee.getId());

      if (attendance == null) {
        continue;
      }

      // 1. Auto-checkout nếu có checkin nhưng chưa checkout
      if (attendance.getCheckIn() != null && attendance.getCheckOut() == null) {
        if (attendance.getCheckinStatus() != CheckinStatus.APPROVED_LEAVE &&
            attendance.getCheckinStatus() != CheckinStatus.UNEXPLAINED_ABSENCE) {

          CheckoutStatus checkoutStatus = CheckoutStatus.FORGOT_CHECK_OUT;

          log.info("Auto-checking out employee {}, status = {}", employee.getFullName(),
              checkoutStatus);

          attendance.setCheckOut(now);
          attendance.setAutoCheckout(true);
          attendance.setCheckoutStatus(checkoutStatus);
        }
      }

      // 2. Dù là absent, leave hay đi làm → đều tính giờ làm
      calculateWorkingHoursSummary(attendance, employee.getId());
      toUpdate.add(attendance);
    }

    if (!toUpdate.isEmpty()) {
      OvertimeSetting overtimeSetting = overtimeSettingRepository
          .findTopByEffectiveDateLessThanEqualOrderByEffectiveDateDesc(LocalDate.now());
      List<AttendanceBasicDto> attendanceBasicDtos = toUpdate.stream()
          .map(attendance -> {
            if (attendance.getCheckinStatus() == CheckinStatus.LATE) {
              int minutesLate = DateTimeUtils.calculateMinutesBetween(
                  attendanceSetting.getStandardCheckIn(), attendance.getCheckIn());
              BigDecimal lateDeductionRate = attendanceSetting.getLateDeductionRate().multiply(
                  BigDecimal.valueOf(minutesLate / attendanceSetting.getMinutePerDeduction()));
              attendance.setTotalLateDeductionRate(lateDeductionRate);
            } else {
              attendance.setTotalLateDeductionRate(BigDecimal.ZERO);
            }
            if (attendance.getCheckoutStatus() == CheckoutStatus.EARLY_OUT) {
              int minutesEarlyOut = DateTimeUtils.calculateMinutesBetween(attendance.getCheckOut(),
                  attendanceSetting.getStandardCheckOut());
              BigDecimal earlyOutDeductionRate = attendanceSetting.getEarlyOutDeductionRate()
                  .multiply(BigDecimal.valueOf(
                      minutesEarlyOut / attendanceSetting.getMinutePerDeduction()));
              attendance.setTotalEarlyOutDeductionRate(earlyOutDeductionRate);
            } else {
              attendance.setTotalEarlyOutDeductionRate(BigDecimal.ZERO);
            }
            AttendanceBasicDto attendanceBasicDto = attendanceMapper.toBasicDto(attendance);
            if (dayType == DayType.HOLIDAY) {
              attendanceBasicDto.setOvertimeRate(overtimeSetting.getHolidayRate());
            } else {
              attendanceBasicDto.setOvertimeRate(overtimeSetting.getWeekdayRate());
            }
            return attendanceBasicDto;
          })
          .toList();
      AttendanceBasicWrapperDto wrapper = new AttendanceBasicWrapperDto();
      wrapper.setAttendances(attendanceBasicDtos);

      kafkaTimeProducer.sendWhenTodayWorkingHoursCalculated(wrapper);

      attendanceRepository.saveAll(toUpdate);
      log.info("===> [END] Auto check out all and calculate working hours done");
    }
  }


  public void calculateWorkingHoursSummary(Attendance attendance, UUID employeeId) {
    if (attendance.getCheckIn() == null || attendance.getCheckOut() == null
        || attendance.getCheckoutStatus() == CheckoutStatus.FORGOT_CHECK_OUT) {
      attendance.setStandardWorkingHours(BigDecimal.ZERO);
      attendance.setOvertimeHours(BigDecimal.ZERO);
      return;
    }

    AttendanceSetting attendanceSetting = attendanceSettingRepository
        .findTopByWorkTypeAndEffectiveDateLessThanEqualOrderByEffectiveDateDesc(
            attendance.getEmployeeCache().getWorkType(), attendance.getDate()
        );

    OvertimeRequest overtimeRequest = overtimeRequestRepository
        .findByEmployeeCache_IdAndDate(employeeId, attendance.getDate())
        .orElse(null);

    BigDecimal totalHours = DateTimeUtils.calculateHoursBetween(
        attendance.getCheckIn(), attendance.getCheckOut());

    BigDecimal standardHours = totalHours.min(attendanceSetting.getStandardWorkingHours());
    BigDecimal overtimeHours = BigDecimal.ZERO;

    if (overtimeRequest != null && overtimeRequest.getStatus() == ApproveStatus.APPROVED) {
      BigDecimal requestedOvertime = overtimeRequest.getTotalHours();
      BigDecimal actualExtra = totalHours.subtract(attendanceSetting.getStandardWorkingHours());

      if (actualExtra.compareTo(BigDecimal.ZERO) >= 0) {
        overtimeHours = actualExtra.min(requestedOvertime);
      }
    }

    attendance.setStandardWorkingHours(standardHours);
    attendance.setOvertimeHours(overtimeHours);
  }


}
