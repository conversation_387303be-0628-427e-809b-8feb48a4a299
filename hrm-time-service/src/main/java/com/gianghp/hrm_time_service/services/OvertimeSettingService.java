package com.gianghp.hrm_time_service.services;

import com.gianghp.hrm_time_service.dtos.post.OvertimeSettingCreateDto;
import com.gianghp.hrm_time_service.dtos.get.OvertimeSettingDto;
import com.gianghp.hrm_time_service.mappers.OvertimeSettingMapper;
import com.gianghp.hrm_time_service.repositories.OvertimeSettingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class OvertimeSettingService {

    private final OvertimeSettingRepository overtimeSettingRepository;
    private final OvertimeSettingMapper overtimeSettingMapper;

    public List<OvertimeSettingDto> findAll() {
        return overtimeSettingRepository.findAll().stream()
                .map(overtimeSettingMapper::toDto)
                .toList();
    }

    public OvertimeSettingDto findById(UUID id) {
        return overtimeSettingRepository.findById(id).map(overtimeSettingMapper::toDto).orElseThrow(() -> new RuntimeException("Overtime setting not found"));
    }

    public OvertimeSettingDto getCurrentSetting() {
        return overtimeSettingMapper.toDto(overtimeSettingRepository.findTopByEffectiveDateLessThanEqualOrderByEffectiveDateDesc(LocalDate.now()));
    }

    public OvertimeSettingDto create(OvertimeSettingCreateDto createDto) {
        return overtimeSettingMapper.toDto(overtimeSettingRepository.save(overtimeSettingMapper.toEntity(createDto)));
    }

}
