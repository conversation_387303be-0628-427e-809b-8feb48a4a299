package com.gianghp.hrm_time_service.services;

import com.gianghp.hrm_time_service.dtos.get.LeaveTypeDto;
import com.gianghp.hrm_time_service.mappers.LeaveTypeMapper;
import com.gianghp.hrm_time_service.repositories.LeaveTypeRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class LeaveTypeService {

    private final LeaveTypeRepository leaveTypeRepository;
    private final LeaveTypeMapper leaveTypeMapper;

    public List<LeaveTypeDto> findAll() {
        return leaveTypeRepository.findAll().stream()
                .map(leaveTypeMapper::toDto)
                .toList();
    }

    public List<LeaveTypeDto> findActiveLeaveTypes() {
        return leaveTypeRepository.findByIsActiveTrue().stream()
                .map(leaveTypeMapper::toDto)
                .toList();
    }

    public List<LeaveTypeDto> findInactiveLeaveTypes() {
        return leaveTypeRepository.findByIsActiveFalse().stream()
                .map(leaveTypeMapper::toDto)
                .toList();
    }

    public void activateBatch(List<UUID> idList) {
        if (idList != null && !idList.isEmpty()) {
            leaveTypeRepository.activateAllByIds(idList);
        }
    }

    public void deactivateBatch(List<UUID> idList) {
        if (idList != null && !idList.isEmpty()) {
            leaveTypeRepository.deactivateAllByIds(idList);
        }
    }

}
