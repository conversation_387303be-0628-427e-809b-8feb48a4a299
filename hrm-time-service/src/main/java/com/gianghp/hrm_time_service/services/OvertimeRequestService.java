package com.gianghp.hrm_time_service.services;

import com.gianghp.hrm.dtos.ApprovedOvertimeBasicDto;
import com.gianghp.hrm.enums.ApproveStatus;
import com.gianghp.hrm.utils.DateTimeUtils;
import com.gianghp.hrm_time_service.dtos.post.EmployeeOvertimeRequestCreateDto;
import com.gianghp.hrm_time_service.dtos.post.OvertimeRequestCreateDto;
import com.gianghp.hrm_time_service.dtos.get.OvertimeRequestDto;
import com.gianghp.hrm_time_service.dtos.get.OvertimeSummaryThisMonth;
import com.gianghp.hrm_time_service.dtos.get.OvertimeThisMonthDto;
import com.gianghp.hrm_time_service.dtos.params.AdminOvertimeRequestParams;
import com.gianghp.hrm_time_service.dtos.params.ManagerOvertimeRequestParams;
import com.gianghp.hrm_time_service.dtos.params.MyOvertimeRequestParams;
import com.gianghp.hrm_time_service.dtos.put.OvertimeRequestUpdateDto;
import com.gianghp.hrm_time_service.entities.EmployeeCache;
import com.gianghp.hrm_time_service.entities.OvertimeRequest;
import com.gianghp.hrm_time_service.entities.OvertimeSetting;
import com.gianghp.hrm_time_service.mappers.AdminOvertimeRequestParamsMapper;
import com.gianghp.hrm_time_service.mappers.OvertimeRequestMapper;
import com.gianghp.hrm_time_service.producers.KafkaTimeProducer;
import com.gianghp.hrm_time_service.repositories.EmployeeCacheRepository;
import com.gianghp.hrm_time_service.repositories.HolidayRepository;
import com.gianghp.hrm_time_service.repositories.OvertimeRequestRepository;
import com.gianghp.hrm_time_service.repositories.OvertimeSettingRepository;
import com.gianghp.hrm_time_service.specifications.OvertimeRequestSpecification;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class OvertimeRequestService {

    private final OvertimeRequestRepository overtimeRequestRepository;
    private final OvertimeRequestMapper overtimeRequestMapper;
    private final EmployeeCacheRepository employeeCacheRepository;
    private final OvertimeSettingRepository overtimeSettingRepository;
    private final HolidayRepository holidayRepository;
    private final AdminOvertimeRequestParamsMapper adminOvertimeRequestParamsMapper;
    private final KafkaTimeProducer kafkaTimeProducer;

    public Page<OvertimeRequestDto> findAll(Pageable pageable) {
        return overtimeRequestRepository.findAll(pageable).map(overtimeRequestMapper::toDto);
    }

    public OvertimeRequestDto findById(UUID id) {
        return overtimeRequestRepository.findById(id).map(overtimeRequestMapper::toDto).orElseThrow(() -> new RuntimeException("Overtime request not found"));
    }

    public Page<OvertimeRequestDto> findByEmployeeIdByFilter(
            UUID employeeId,
            Optional<LocalDate> start,
            Optional<LocalDate> end,
            Optional<YearMonth> month,
            Pageable pageable
    ) {
        LocalDateTime startDateTime = null;
        LocalDateTime endDateTime = null;

        if (start.isPresent()) {
            startDateTime = start.get().atStartOfDay();
            endDateTime = end
                    .map(e -> e.atTime(LocalTime.MAX))
                    .orElse(start.get().withDayOfMonth(start.get().lengthOfMonth()).atTime(LocalTime.MAX));
        } else if (month.isPresent()) {
            YearMonth ym = month.get();
            startDateTime = ym.atDay(1).atStartOfDay();
            endDateTime = ym.atEndOfMonth().atTime(LocalTime.MAX);
        }

        if (startDateTime != null) {
            return overtimeRequestRepository
                    .findByEmployeeCache_IdAndCreatedAtBetween(employeeId, startDateTime, endDateTime, pageable)
                    .map(overtimeRequestMapper::toDto);
        }

        return overtimeRequestRepository
                .findByEmployeeCache_Id(employeeId, pageable)
                .map(overtimeRequestMapper::toDto);
    }

    public OvertimeThisMonthDto getOvertimeThisMonth(UUID employeeId) {
        LocalDate today = LocalDate.now();
        LocalDate startOfMonth = today.withDayOfMonth(1);
        LocalDate endOfMonth = today.withDayOfMonth(today.lengthOfMonth());
        List<OvertimeRequest> overtimeRequests = overtimeRequestRepository.findByEmployeeCache_IdAndDateBetween(employeeId, startOfMonth, endOfMonth);
        int approved = (int) overtimeRequests.stream().filter(ot -> ot.getStatus() == ApproveStatus.APPROVED).count();
        int pending = (int) overtimeRequests.stream().filter(ot -> ot.getStatus() == ApproveStatus.PENDING).count();
        int rejected = (int) overtimeRequests.stream().filter(ot -> ot.getStatus() == ApproveStatus.REJECTED).count();
        BigDecimal totalHours = overtimeRequests.stream()
                .map(this::calculateOvertimeHours)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return new OvertimeThisMonthDto(approved, pending, rejected, totalHours.intValue());
    }



    public Page<OvertimeRequestDto> findByStatus(ApproveStatus status, Pageable pageable) {
        return overtimeRequestRepository.findByStatus(status, pageable).map(overtimeRequestMapper::toDto);
    }


    public OvertimeRequestDto create(OvertimeRequestCreateDto createDto) {
        EmployeeCache employeeCache = employeeCacheRepository.findById(createDto.getEmployeeId()).orElseThrow(() -> new RuntimeException("Employee not found"));
        OvertimeSetting overtimeSetting = overtimeSettingRepository.findTopByEffectiveDateLessThanEqualOrderByEffectiveDateDesc(LocalDate.now());
        EmployeeCache approver = employeeCacheRepository.findById(createDto.getApproverId()).orElse(null);
        OvertimeRequest overtimeRequest = overtimeRequestMapper.toEntity(createDto);
        overtimeRequest.setEmployeeCache(employeeCache);
        overtimeRequest.setOvertimeSetting(overtimeSetting);
        overtimeRequest.setApprover(approver);
        if (createDto.getStatus() == null) {
            overtimeRequest.setStatus(ApproveStatus.PENDING);
        }
        overtimeRequestRepository.save(overtimeRequest);
        return overtimeRequestMapper.toDto(overtimeRequest);
    }

    public OvertimeRequestDto update(UUID id, OvertimeRequestUpdateDto updateDto) {
        OvertimeRequest overtimeRequest = overtimeRequestRepository.findById(id).orElseThrow(() -> new RuntimeException("Overtime request not found"));
        if (overtimeRequest.getStatus() != ApproveStatus.PENDING) {
            throw new RuntimeException("Overtime request cannot be updated");
        }
        overtimeRequestMapper.updateEntity(updateDto, overtimeRequest);
        overtimeRequestRepository.save(overtimeRequest);
        return overtimeRequestMapper.toDto(overtimeRequest);
    }

    public void approve(UUID id, UUID approverId) {
        OvertimeRequest request = overtimeRequestRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("Overtime request not found"));

        if (request.getStatus() != ApproveStatus.PENDING) {
            throw new RuntimeException("Overtime request cannot be approved");
        }
        if (request.getApprover() != null && !request.getApprover().getUserId().equals(approverId)) {
            throw new RuntimeException("Overtime request cannot be approved by another approver");
        }

        EmployeeCache approver = employeeCacheRepository.findByUserId(approverId)
            .orElseThrow(() -> new RuntimeException("Approver not found"));

        request.setStatus(ApproveStatus.APPROVED);
        request.setApprover(approver);

        ApprovedOvertimeBasicDto dto = buildApprovedDto(request);
        kafkaTimeProducer.sendWhenOvertimeApproved(dto);

        overtimeRequestRepository.save(request);
    }

    private ApprovedOvertimeBasicDto buildApprovedDto(OvertimeRequest request) {
        ApprovedOvertimeBasicDto dto = new ApprovedOvertimeBasicDto();
        dto.setId(request.getId());
        dto.setEmployeeId(request.getEmployeeCache().getUserId());
        dto.setDate(request.getDate());
        dto.setTotalHours(request.getTotalHours());
        dto.setRate(resolveRate(request));
        return dto;
    }

    private BigDecimal resolveRate(OvertimeRequest request) {
        LocalDate date = request.getDate();
        OvertimeSetting setting = request.getOvertimeSetting();

        if (holidayRepository.existsByDate(date)) {
            return setting.getHolidayRate();
        }
        else {
            return setting.getWeekdayRate();
        }
    }


    public void reject(UUID id, UUID approverId) {
        OvertimeRequest overtimeRequest = overtimeRequestRepository.findById(id).orElseThrow(() -> new RuntimeException("Overtime request not found"));
        if (overtimeRequest.getStatus() != ApproveStatus.PENDING) {
            throw new RuntimeException("Overtime request cannot be rejected");
        }
        if (overtimeRequest.getApprover() != null && !overtimeRequest.getApprover().getUserId().equals(approverId)) {
            throw new RuntimeException("Overtime request cannot be rejected by another approver");
        }
        overtimeRequest.setStatus(ApproveStatus.REJECTED);
        overtimeRequest.setApprover(employeeCacheRepository.findByUserId(approverId).orElseThrow(() -> new RuntimeException("Approver not found")));
        overtimeRequestRepository.save(overtimeRequest);
    }

    public BigDecimal calculateOvertimeHoursThisMonth(UUID employeeId) {
        LocalDate today = LocalDate.now();
        LocalDate startOfMonth = today.withDayOfMonth(1);
        LocalDate endOfMonth = today.withDayOfMonth(today.lengthOfMonth());
        List<OvertimeRequest> overtimeRequests = overtimeRequestRepository.findByEmployeeCache_IdAndDateBetween(employeeId, startOfMonth, endOfMonth);
        return overtimeRequests.stream()
                .map(this::calculateOvertimeHours)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public OvertimeSummaryThisMonth getOvertimeSummaryThisMonth(UUID employeeId) {
        LocalDate today = LocalDate.now();
        LocalDate startOfMonth = today.withDayOfMonth(1);
        LocalDate endOfMonth = today.withDayOfMonth(today.lengthOfMonth());
        List<OvertimeRequest> overtimeRequests = overtimeRequestRepository.findByEmployeeCache_IdAndDateBetween(employeeId, startOfMonth, endOfMonth);
        BigDecimal totalHours = overtimeRequests.stream()
                .map(this::calculateOvertimeHours)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        int pending = (int) overtimeRequests.stream().filter(ot -> ot.getStatus() == ApproveStatus.PENDING).count();
        int totalRecords = overtimeRequests.size();

        OvertimeSummaryThisMonth overtimeSummaryThisMonth = new OvertimeSummaryThisMonth();
        overtimeSummaryThisMonth.setTotalHours(totalHours);
        overtimeSummaryThisMonth.setTotalPending(pending);
        overtimeSummaryThisMonth.setTotalRecords(totalRecords);
        return overtimeSummaryThisMonth;
    }

    public BigDecimal calculateOvertimeHours(OvertimeRequest overtimeRequest) {
        // Since we now store totalHours directly, just return it
        return overtimeRequest.getTotalHours() != null ? overtimeRequest.getTotalHours() : BigDecimal.ZERO;
    }

    public Page<OvertimeRequestDto> findByParams(AdminOvertimeRequestParams params) {
        log.info("sort by: {}, sort dir: {}", params.getSortBy(), params.getSortDir());
        Pageable pageable = PageRequest.of(
                params.getPage(),
                params.getSize(),
                params.getSortDir().equalsIgnoreCase("desc")
                        ? Sort.by(params.getSortBy()).descending()
                        : Sort.by(params.getSortBy()).ascending()
        );

        // Handle month and year parameters
        if (params.getMonth() != null && !params.getMonth().isBlank()) {
            YearMonth ym = YearMonth.parse(params.getMonth());
            params.setStartDate(ym.atDay(1));
            params.setEndDate(ym.atEndOfMonth());
        }

        // Handle special filters
        if (params.getIsCurrentMonth() != null && params.getIsCurrentMonth()) {
            LocalDate now = LocalDate.now();
            params.setStartDate(now.withDayOfMonth(1));
            params.setEndDate(now.withDayOfMonth(now.lengthOfMonth()));
        }

        if (params.getIsCurrentYear() != null && params.getIsCurrentYear()) {
            int currentYear = LocalDate.now().getYear();
            params.setStartDate(LocalDate.of(currentYear, 1, 1));
            params.setEndDate(LocalDate.of(currentYear, 12, 31));
        }

        // Handle status filters
        if (params.getIsApproved() != null && params.getIsApproved()) {
            params.setStatus(ApproveStatus.APPROVED);
        } else if (params.getIsPending() != null && params.getIsPending()) {
            params.setStatus(ApproveStatus.PENDING);
        } else if (params.getIsRejected() != null && params.getIsRejected()) {
            params.setStatus(ApproveStatus.REJECTED);
        }

        // Use JPA Specifications for filtering
        Specification<OvertimeRequest> spec = OvertimeRequestSpecification.buildSpecification(params);
        return overtimeRequestRepository.findAll(spec, pageable).map(overtimeRequestMapper::toDto);
    }

    public Page<OvertimeRequestDto> findMyByParams(UUID userId, MyOvertimeRequestParams myParams) {
        EmployeeCache employee = employeeCacheRepository.findByUserId(userId).orElseThrow(() -> new RuntimeException("Employee not found"));
        AdminOvertimeRequestParams params = adminOvertimeRequestParamsMapper.fromMyParams(myParams);
        params.setEmployeeId(employee.getId());
        params.setEmployeeCode(employee.getEmployeeCode());
        params.setEmployeeName(employee.getFullName());
        params.setDepartmentId(employee.getDepartmentId());
        return findByParams(params);
    }


    public Page<OvertimeRequestDto> findByManagerParams(UUID userId, ManagerOvertimeRequestParams managerParams) {
        EmployeeCache employee = employeeCacheRepository.findByUserId(userId).orElseThrow(() -> new RuntimeException("Employee not found"));
        AdminOvertimeRequestParams params = adminOvertimeRequestParamsMapper.fromManagerParams(managerParams);
        params.setDepartmentId(employee.getDepartmentId());
        return findByParams(params);
    }

    public OvertimeSummaryThisMonth getMyOvertimeSummaryThisMonth(UUID userId) {
        EmployeeCache employee = employeeCacheRepository.findByUserId(userId).orElseThrow(() -> new RuntimeException("Employee not found"));
        return getOvertimeSummaryThisMonth(employee.getId());
    }

    public OvertimeRequestDto createMyOvertimeRequest(UUID userId, EmployeeOvertimeRequestCreateDto createDto) {
        EmployeeCache employee = employeeCacheRepository.findByUserId(userId).orElseThrow(() -> new RuntimeException("Employee not found"));
        OvertimeSetting overtimeSetting = overtimeSettingRepository.findTopByEffectiveDateLessThanEqualOrderByEffectiveDateDesc(LocalDate.now());
        OvertimeRequest overtimeRequest = overtimeRequestMapper.toEntity(createDto);
        overtimeRequest.setEmployeeCache(employee);
        overtimeRequest.setOvertimeSetting(overtimeSetting);
        if (createDto.getStatus() == null) {
            overtimeRequest.setStatus(ApproveStatus.PENDING);
        }
        overtimeRequestRepository.save(overtimeRequest);
        return overtimeRequestMapper.toDto(overtimeRequest);
    }
}
