package com.gianghp.hrm_time_service.services;

import com.gianghp.hrm.enums.WorkType;
import com.gianghp.hrm_time_service.entities.AttendanceSetting;
import com.gianghp.hrm_time_service.repositories.AttendanceSettingRepository;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.stereotype.Service;

import java.time.*;
import java.util.concurrent.ScheduledFuture;

@Service
@RequiredArgsConstructor
public class DynamicSchedulerService {

    private final AttendanceService attendanceService;
    private final AttendanceSettingRepository settingRepo;
    private final TaskScheduler taskScheduler;

    private ScheduledFuture<?> autoCheckoutTask;
    private ScheduledFuture<?> autoAbsentTask;

    @PostConstruct
    public void init() {
        scheduleBasedOnSetting();
        scheduleAutoAbsent();
    }

    public void scheduleBasedOnSetting() {
        scheduleAutoCheckout();
        scheduleAutoAbsent();
    }

    private void scheduleAutoCheckout() {
        // Hủy job cũ nếu có
        if (autoCheckoutTask != null && !autoCheckoutTask.isCancelled()) {
            autoCheckoutTask.cancel(false);
        }

        // Lấy setting
        AttendanceSetting setting = settingRepo.findTopByWorkTypeAndEffectiveDateLessThanEqualOrderByEffectiveDateDesc(
                WorkType.FULL_TIME, LocalDate.now());
        LocalTime endTime = setting.getCheckOutEnd();

        // Tính thời gian chạy
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime runAt = now.withHour(endTime.getHour()).withMinute(endTime.getMinute()).withSecond(0);
        if (runAt.isBefore(now)) {
            runAt = runAt.plusDays(1);
        }

        Instant runInstant = runAt.atZone(ZoneId.systemDefault()).toInstant();

        // Đặt lịch và lưu lại ScheduledFuture
        autoCheckoutTask = taskScheduler.schedule(
                attendanceService::autoCheckOutAllAndCalculateWorkingHours,
                runInstant
        );
    }

    private void scheduleAutoAbsent() {
        // Hủy job cũ nếu có
        if (autoAbsentTask != null && !autoAbsentTask.isCancelled()) {
            autoAbsentTask.cancel(false);
        }

        // Lấy setting
        AttendanceSetting setting = settingRepo.findTopByWorkTypeAndEffectiveDateLessThanEqualOrderByEffectiveDateDesc(
                WorkType.FULL_TIME, LocalDate.now());
        LocalTime checkInEndTime = setting.getCheckInEnd();

        // Tính thời gian chạy (sau khi hết thời gian check-in)
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime runAt = now.withHour(checkInEndTime.getHour()).withMinute(checkInEndTime.getMinute()).withSecond(0);
        if (runAt.isBefore(now)) {
            runAt = runAt.plusDays(1);
        }

        Instant runInstant = runAt.atZone(ZoneId.systemDefault()).toInstant();

        // Đặt lịch và lưu lại ScheduledFuture
        autoAbsentTask = taskScheduler.schedule(
                attendanceService::autoMarkAbsentAll,
                runInstant
        );
    }
}

