package com.gianghp.hrm_time_service.repositories;

import com.gianghp.hrm_time_service.entities.EmployeeCache;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface EmployeeCacheRepository extends JpaRepository<EmployeeCache, UUID> {
    boolean existsByEmployeeCode(String employeeCode);

    Optional<EmployeeCache> findByUserId(UUID userId);
}
