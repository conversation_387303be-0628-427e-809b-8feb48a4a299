package com.gianghp.hrm_time_service.repositories;

import com.gianghp.hrm.enums.WorkType;
import com.gianghp.hrm_time_service.entities.AttendanceSetting;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.UUID;

@Repository
public interface AttendanceSettingRepository extends JpaRepository<AttendanceSetting, UUID> {
    AttendanceSetting findTopByWorkTypeAndEffectiveDateLessThanEqualOrderByEffectiveDateDesc(WorkType workType, LocalDate now);

}
