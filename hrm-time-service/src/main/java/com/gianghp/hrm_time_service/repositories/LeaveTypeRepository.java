package com.gianghp.hrm_time_service.repositories;

import com.gianghp.hrm_time_service.entities.LeaveType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

@Repository
public interface LeaveTypeRepository extends JpaRepository<LeaveType, UUID> {
    List<LeaveType> findByIsActiveTrue();

    @Modifying
    @Query("UPDATE LeaveType lt SET lt.isActive = true WHERE lt.id IN :ids")
    void activateAllByIds(@Param("ids") List<UUID> ids);

    @Modifying
    @Query("UPDATE LeaveType lt SET lt.isActive = false WHERE lt.id IN :ids")
    void deactivateAllByIds(@Param("ids") List<UUID> ids);

    List<LeaveType> findByIsActiveFalse();
}
