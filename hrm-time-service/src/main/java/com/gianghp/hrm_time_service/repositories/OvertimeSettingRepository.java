package com.gianghp.hrm_time_service.repositories;

import com.gianghp.hrm_time_service.entities.OvertimeSetting;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface OvertimeSettingRepository extends JpaRepository<OvertimeSetting, UUID> {
    OvertimeSetting findTopByEffectiveDateLessThanEqualOrderByEffectiveDateDesc(LocalDate now);

}
