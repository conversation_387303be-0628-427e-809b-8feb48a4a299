package com.gianghp.hrm_time_service.repositories;


import com.gianghp.hrm.enums.WorkType;
import com.gianghp.hrm_time_service.entities.Attendance;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface AttendanceRepository extends JpaRepository<Attendance, UUID>, JpaSpecificationExecutor<Attendance> {

    Page<Attendance> findByEmployeeCache_Id(UUID employeeId, Pageable pageable);

    List<Attendance> findByEmployeeCache_IdAndDateBetween(UUID employeeId, LocalDate startDate, LocalDate endDate);

    Page<Attendance> findByEmployeeCache_IdAndDateBetween(UUID employeeId, LocalDate startDate, LocalDate endDate, Pageable pageable);

    Page<Attendance> findByEmployeeCache_DepartmentId(UUID departmentId, Pageable pageable);

    boolean existsByEmployeeCache_IdAndDate(UUID employeeId, LocalDate now);

    Page<Attendance> findByEmployeeCache_IdAndCheckInBetween(UUID employeeId, LocalTime startDateTime, LocalTime endDateTime, Pageable pageable);

    List<Attendance> findByEmployeeCache_IdAndCheckInBetween(UUID employeeId, LocalTime startDateTime, LocalTime endDateTime);

    Attendance findFirstByEmployeeCache_IdAndCheckInBetween(UUID employeeId, LocalTime startOfDay, LocalTime endOfDay);

    Attendance findFirstByEmployeeCache_IdAndDate(UUID employeeId, LocalDate today);

  List<Attendance> findByDate(LocalDate now);

  // JpaSpecificationExecutor provides findAll(Specification<T> spec, Pageable pageable) method
}
