package com.gianghp.hrm_time_service.repositories;

import com.gianghp.hrm.enums.LeaveRequestStatus;
import com.gianghp.hrm.enums.WorkType;
import com.gianghp.hrm_time_service.entities.LeaveRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.data.domain.Page;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface LeaveRequestRepository extends JpaRepository<LeaveRequest, UUID>, JpaSpecificationExecutor<LeaveRequest> {
    Page<LeaveRequest> findByEmployeeCache_Id(UUID employeeId, Pageable pageable);

    Page<LeaveRequest> findByStatus(LeaveRequestStatus status, Pageable pageable);
    Page<LeaveRequest> findByStartDateBetween(LocalDate startDate, LocalDate endDate, Pageable pageable);

    Page<LeaveRequest> findByEmployeeCache_DepartmentId(UUID departmentId, Pageable pageable);

    Page<LeaveRequest> findByEmployeeCache_IdAndCreatedAtBetween(UUID employeeId, LocalDateTime start, LocalDateTime end, Pageable pageable);

    List<LeaveRequest> findByEmployeeCache_IdAndStartDateBetween(UUID employeeId, LocalDate startOfMonth, LocalDate endOfMonth);

    List<LeaveRequest> findByEmployeeCache_IdAndStatusAndStartDateBetween(UUID employeeId, LeaveRequestStatus status, LocalDate startOfMonth, LocalDate endOfMonth);

    Page<LeaveRequest> findByEmployeeCache_IdAndStartDateBetween(UUID employeeId, LocalDateTime startDateTime, LocalDateTime endDateTime, Pageable pageable);

    Optional<LeaveRequest> findByEmployeeCache_IdAndStartDateLessThanEqualAndEndDateGreaterThanEqualAndStatus(UUID id, LocalDate today, LocalDate today1, LeaveRequestStatus leaveRequestStatus);

    boolean existsByEmployeeCache_IdAndStartDateLessThanEqualAndEndDateGreaterThanEqualAndStatus(
      UUID id, LocalDate today, LocalDate today1, LeaveRequestStatus leaveRequestStatus);

    boolean existsByEmployeeCache_IdAndStatusAndStartDateLessThanEqualAndEndDateGreaterThanEqual(
        UUID id, LeaveRequestStatus leaveRequestStatus, LocalDate startDate, LocalDate endDate);

    // JpaSpecificationExecutor provides findAll(Specification<T> spec, Pageable pageable) method

}
