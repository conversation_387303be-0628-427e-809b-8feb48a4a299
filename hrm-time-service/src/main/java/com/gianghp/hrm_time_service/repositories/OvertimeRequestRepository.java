package com.gianghp.hrm_time_service.repositories;

import com.gianghp.hrm.enums.ApproveStatus;
import com.gianghp.hrm.enums.WorkType;
import com.gianghp.hrm_time_service.entities.OvertimeRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface OvertimeRequestRepository extends JpaRepository<OvertimeRequest, UUID>, JpaSpecificationExecutor<OvertimeRequest> {
    Page<OvertimeRequest> findByEmployeeCache_Id(UUID employeeId, Pageable pageable);

    Page<OvertimeRequest> findByStatus(ApproveStatus status, Pageable pageable);

    Page<OvertimeRequest> findByEmployeeCache_IdAndCreatedAtBetween(
            UUID employeeId,
            LocalDateTime start,
            LocalDateTime end,
            Pageable pageable
    );

    List<OvertimeRequest> findByEmployeeCache_IdAndDateBetween(UUID employeeId, LocalDate startOfMonth, LocalDate endOfMonth);

  List<OvertimeRequest> findByEmployeeCache_IdAndDateBetweenAndStatus(UUID employeeId, LocalDate startDate, LocalDate endDate, ApproveStatus approveStatus);

    Optional<OvertimeRequest> findByEmployeeCache_IdAndDate(UUID employeeId, LocalDate date);

    // JpaSpecificationExecutor provides findAll(Specification<T> spec, Pageable pageable) method
}
