package com.gianghp.hrm_time_service.repositories;

import com.gianghp.hrm_time_service.entities.Holiday;
import java.time.LocalDate;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.UUID;

@Repository
public interface HolidayRepository extends JpaRepository<Holiday, UUID> {

  boolean existsByDate(LocalDate date);

  List<Holiday> findByDateLessThanEqualOrderByDateDesc(LocalDate date);
}
