package com.gianghp.hrm_time_service.mappers;

import com.gianghp.hrm.dtos.AttendanceBasicDto;
import com.gianghp.hrm.dtos.EmployeeBasicDto;
import com.gianghp.hrm_time_service.dtos.get.AttendanceDto;
import com.gianghp.hrm_time_service.dtos.put.AttendanceUpdateDto;
import com.gianghp.hrm_time_service.entities.Attendance;
import com.gianghp.hrm_time_service.entities.EmployeeCache;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface AttendanceMapper {
    AttendanceMapper INSTANCE = Mappers.getMapper(AttendanceMapper.class);

    @Mapping(target = "updatedAt", ignore = true)
    EmployeeCache toEntity(EmployeeBasicDto dto);

    @Mapping(target = "employeeId", expression = "java(attendance.getEmployeeCache().getId())")
    @Mapping(target = "employeeCode", expression = "java(attendance.getEmployeeCache().getEmployeeCode())")
    @Mapping(target = "fullName", expression = "java(attendance.getEmployeeCache().getFullName())")
    AttendanceDto toDto(Attendance attendance);

    @Mapping(target = "employeeCache", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    void updateEntity(AttendanceUpdateDto updateDto, @MappingTarget Attendance attendance);

    List<AttendanceDto> toDtoList(List<Attendance> attendances);

    @Mapping(target = "employeeId", expression = "java(attendance.getEmployeeCache().getId())")
    AttendanceBasicDto toBasicDto(Attendance attendance);


}
