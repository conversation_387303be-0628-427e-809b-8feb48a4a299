package com.gianghp.hrm_time_service.mappers;

import com.gianghp.hrm_time_service.dtos.params.AdminOvertimeRequestParams;
import com.gianghp.hrm_time_service.dtos.params.MyOvertimeRequestParams;
import com.gianghp.hrm_time_service.dtos.params.ManagerOvertimeRequestParams;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * Mapper for converting various overtime request params to AdminOvertimeRequestParams
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface AdminOvertimeRequestParamsMapper {
    AdminOvertimeRequestParamsMapper INSTANCE = Mappers.getMapper(AdminOvertimeRequestParamsMapper.class);

    /**
     * Convert MyOvertimeRequestParams to AdminOvertimeRequestParams
     * Note: employeeCode and employeeName should be set from authenticated user context
     */
    @Mapping(target = "employeeId", ignore = true)
    @Mapping(target = "employeeCode", ignore = true)
    @Mapping(target = "employeeName", ignore = true)
    @Mapping(target = "departmentId", ignore = true)
    AdminOvertimeRequestParams fromMyParams(MyOvertimeRequestParams myParams);

    /**
     * Convert ManagerOvertimeRequestParams to AdminOvertimeRequestParams
     */
    @Mapping(target = "departmentId", ignore = true)
    AdminOvertimeRequestParams fromManagerParams(ManagerOvertimeRequestParams managerParams);
}
