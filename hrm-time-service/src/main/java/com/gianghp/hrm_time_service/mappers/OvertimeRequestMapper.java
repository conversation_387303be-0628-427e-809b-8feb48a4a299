package com.gianghp.hrm_time_service.mappers;

import com.gianghp.hrm_time_service.dtos.post.EmployeeOvertimeRequestCreateDto;
import com.gianghp.hrm_time_service.dtos.post.OvertimeRequestCreateDto;
import com.gianghp.hrm_time_service.dtos.get.OvertimeRequestDto;
import com.gianghp.hrm_time_service.dtos.put.OvertimeRequestUpdateDto;
import com.gianghp.hrm_time_service.entities.OvertimeRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface OvertimeRequestMapper {
    OvertimeRequestMapper INSTANCE = Mappers.getMapper(OvertimeRequestMapper.class);

    @Mapping(target = "employeeId", expression = "java(overtimeRequest.getEmployeeCache().getId())")
    @Mapping(target = "approverName", expression = "java(overtimeRequest.getApprover() != null ? overtimeRequest.getApprover().getFullName() : null)")
    @Mapping(target = "overtimeSettingId", expression = "java(overtimeRequest.getOvertimeSetting() != null ? overtimeRequest.getOvertimeSetting().getId() : null)")
    @Mapping(target = "employeeName", expression = "java(overtimeRequest.getEmployeeCache().getFullName())")
    @Mapping(target = "employeeCode", expression = "java(overtimeRequest.getEmployeeCache().getEmployeeCode())")
    OvertimeRequestDto toDto(OvertimeRequest overtimeRequest);

    List<OvertimeRequestDto> toDtoList(List<OvertimeRequest> overtimeRequests);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "overtimeSetting", ignore = true)
    @Mapping(target = "status", constant = "PENDING")
    OvertimeRequest toEntity(OvertimeRequestCreateDto createDto);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "overtimeSetting", ignore = true)
    OvertimeRequest toEntity(EmployeeOvertimeRequestCreateDto dto);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "overtimeSetting", ignore = true)
    void updateEntity(OvertimeRequestUpdateDto updateDto, @MappingTarget OvertimeRequest overtimeRequest);
}
