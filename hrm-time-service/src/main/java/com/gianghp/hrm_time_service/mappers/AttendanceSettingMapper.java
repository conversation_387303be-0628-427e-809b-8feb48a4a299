package com.gianghp.hrm_time_service.mappers;

import com.gianghp.hrm.dtos.AttendanceSettingBasicDto;
import com.gianghp.hrm_time_service.dtos.post.AttendanceSettingCreateDto;
import com.gianghp.hrm_time_service.dtos.get.AttendanceSettingDto;
import com.gianghp.hrm_time_service.dtos.put.AttendanceSettingUpdateDto;
import com.gianghp.hrm_time_service.entities.AttendanceSetting;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface AttendanceSettingMapper {
    AttendanceSettingMapper INSTANCE = Mappers.getMapper(AttendanceSettingMapper.class);

    AttendanceSettingDto toDto(AttendanceSetting attendanceSetting);

    List<AttendanceSettingDto> toDtoList(List<AttendanceSetting> attendanceSettings);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    AttendanceSetting toEntity(AttendanceSettingCreateDto createDto);

    AttendanceSettingBasicDto toBasicDto(AttendanceSetting attendanceSetting);
}
