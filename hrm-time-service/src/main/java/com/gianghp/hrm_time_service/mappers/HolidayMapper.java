package com.gianghp.hrm_time_service.mappers;

import com.gianghp.hrm_time_service.dtos.post.HolidayCreateDto;
import com.gianghp.hrm_time_service.dtos.get.HolidayDto;
import com.gianghp.hrm_time_service.entities.Holiday;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface HolidayMapper {
    HolidayMapper INSTANCE = Mappers.getMapper(HolidayMapper.class);

    HolidayDto toDto(Holiday holiday);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    Holiday toEntity(HolidayCreateDto dto);
}
