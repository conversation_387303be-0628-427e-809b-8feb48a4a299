package com.gianghp.hrm_time_service.mappers;

import com.gianghp.hrm_time_service.dtos.post.LeaveTypeCreateDto;
import com.gianghp.hrm_time_service.dtos.get.LeaveTypeDto;
import com.gianghp.hrm_time_service.dtos.put.LeaveTypeUpdateDto;
import com.gianghp.hrm_time_service.entities.LeaveType;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface LeaveTypeMapper {
    LeaveTypeMapper INSTANCE = Mappers.getMapper(LeaveTypeMapper.class);

    @Mapping(target = "active", source = "active")
    LeaveTypeDto toDto(LeaveType leaveType);

    List<LeaveTypeDto> toDtoList(List<LeaveType> leaveTypes);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "active", source = "active")
    LeaveType toEntity(LeaveTypeCreateDto createDto);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    void updateEntity(LeaveTypeUpdateDto updateDto, @MappingTarget LeaveType leaveType);
}
