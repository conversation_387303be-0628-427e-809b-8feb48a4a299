package com.gianghp.hrm_time_service.mappers;

import com.gianghp.hrm_time_service.dtos.post.OvertimeSettingCreateDto;
import com.gianghp.hrm_time_service.dtos.get.OvertimeSettingDto;
import com.gianghp.hrm_time_service.dtos.put.OvertimeSettingUpdateDto;
import com.gianghp.hrm_time_service.entities.OvertimeSetting;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface OvertimeSettingMapper {
    OvertimeSettingMapper INSTANCE = Mappers.getMapper(OvertimeSettingMapper.class);

    OvertimeSettingDto toDto(OvertimeSetting overtimeSetting);

    List<OvertimeSettingDto> toDtoList(List<OvertimeSetting> overtimeSettings);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    OvertimeSetting toEntity(OvertimeSettingCreateDto createDto);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    void updateEntity(OvertimeSettingUpdateDto updateDto, @MappingTarget OvertimeSetting overtimeSetting);
}
