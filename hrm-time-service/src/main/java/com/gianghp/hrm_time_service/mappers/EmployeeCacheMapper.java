package com.gianghp.hrm_time_service.mappers;

import com.gianghp.hrm.dtos.EmployeeBasicDto;
import com.gianghp.hrm_time_service.dtos.get.EmployeeCacheDto;
import com.gianghp.hrm_time_service.entities.EmployeeCache;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface EmployeeCacheMapper {
    EmployeeCacheMapper INSTANCE = Mappers.getMapper(EmployeeCacheMapper.class);

    EmployeeCacheDto toDto(EmployeeCache employeeCache);

    List<EmployeeCacheDto> toDtoList(List<EmployeeCache> employeeCaches);

    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "workType", source = "type")
    EmployeeCache toEntity(EmployeeBasicDto dto);

    @Mapping(target = "updatedAt", ignore = true)
    EmployeeCache toEntity(EmployeeCacheDto dto);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    void updateEntity(EmployeeCacheDto dto, @MappingTarget EmployeeCache employeeCache);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    void updateEntityFromBasicDto(EmployeeBasicDto dto, @MappingTarget EmployeeCache employeeCache);
}
