package com.gianghp.hrm_time_service.mappers;

import com.gianghp.hrm_time_service.dtos.params.AdminAttendanceRequestParams;
import com.gianghp.hrm_time_service.dtos.params.MyAttendanceRequestParams;
import com.gianghp.hrm_time_service.dtos.params.ManagerAttendanceRequestParams;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * Mapper for converting various attendance request params to AdminAttendanceRequestParams
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface AdminAttendanceRequestParamsMapper {
    AdminAttendanceRequestParamsMapper INSTANCE = Mappers.getMapper(AdminAttendanceRequestParamsMapper.class);

    /**
     * Convert MyAttendanceRequestParams to AdminAttendanceRequestParams
     * Note: employeeCode and employeeName should be set from authenticated user context
     */
    @Mapping(target = "employeeId", ignore = true)
    @Mapping(target = "employeeCode", ignore = true)
    @Mapping(target = "employeeName", ignore = true)
    @Mapping(target = "departmentId", ignore = true)
    AdminAttendanceRequestParams fromMyParams(MyAttendanceRequestParams myParams);

    /**
     * Convert ManagerAttendanceRequestParams to AdminAttendanceRequestParams
     */
    @Mapping(target = "departmentId", ignore = true)
    AdminAttendanceRequestParams fromManagerParams(ManagerAttendanceRequestParams managerParams);
}
