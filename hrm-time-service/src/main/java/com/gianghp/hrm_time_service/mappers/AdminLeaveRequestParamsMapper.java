package com.gianghp.hrm_time_service.mappers;

import com.gianghp.hrm_time_service.dtos.params.AdminLeaveRequestParams;
import com.gianghp.hrm_time_service.dtos.params.MyLeaveRequestParams;
import com.gianghp.hrm_time_service.dtos.params.ManagerLeaveRequestParams;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * Mapper for converting various leave request params to AdminLeaveRequestParams
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface AdminLeaveRequestParamsMapper {
    AdminLeaveRequestParamsMapper INSTANCE = Mappers.getMapper(AdminLeaveRequestParamsMapper.class);

    /**
     * Convert MyLeaveRequestParams to AdminLeaveRequestParams
     * Note: employeeCode and employeeName should be set from authenticated user context
     */
    @Mapping(target = "employeeId", ignore = true)
    @Mapping(target = "employeeCode", ignore = true)
    @Mapping(target = "employeeName", ignore = true)
    @Mapping(target = "departmentId", ignore = true)
    AdminLeaveRequestParams fromMyParams(MyLeaveRequestParams myParams);

    /**
     * Convert ManagerLeaveRequestParams to AdminLeaveRequestParams
     */
    @Mapping(target = "departmentId", ignore = true)
    AdminLeaveRequestParams fromManagerParams(ManagerLeaveRequestParams managerParams);
}
