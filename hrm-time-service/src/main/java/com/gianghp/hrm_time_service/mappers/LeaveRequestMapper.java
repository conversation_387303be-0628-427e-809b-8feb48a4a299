package com.gianghp.hrm_time_service.mappers;

import com.gianghp.hrm_time_service.dtos.post.EmployeeLeaveRequestCreateDto;
import com.gianghp.hrm_time_service.dtos.post.LeaveRequestCreateDto;
import com.gianghp.hrm_time_service.dtos.get.LeaveRequestDto;
import com.gianghp.hrm_time_service.dtos.put.LeaveRequestUpdateDto;
import com.gianghp.hrm_time_service.entities.LeaveRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface LeaveRequestMapper {
    LeaveRequestMapper INSTANCE = Mappers.getMapper(LeaveRequestMapper.class);

    @Mapping(target = "employeeId", expression = "java(leaveRequest.getEmployeeCache().getId())")
    @Mapping(target = "employeeName", expression = "java(leaveRequest.getEmployeeCache().getFullName())")
    @Mapping(target = "employeeCode", expression = "java(leaveRequest.getEmployeeCache().getEmployeeCode())")
    @Mapping(target = "leaveTypeId", expression = "java(leaveRequest.getLeaveType().getId())")
    @Mapping(target = "approverId", expression = "java(leaveRequest.getApprover() != null ? leaveRequest.getApprover().getId() : null)")
    @Mapping(target = "approverName", expression = "java(leaveRequest.getApprover() != null ? leaveRequest.getApprover().getFullName() : null)")
    @Mapping(target = "leaveTypeName", expression = "java(leaveRequest.getLeaveType().getName())")
    LeaveRequestDto toDto(LeaveRequest leaveRequest);

    List<LeaveRequestDto> toDtoList(List<LeaveRequest> leaveRequests);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "employeeCache", ignore = true)
    @Mapping(target = "leaveType", ignore = true)
    LeaveRequest toEntity(LeaveRequestCreateDto createDto);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "employeeCache", ignore = true)
    @Mapping(target = "leaveType", ignore = true)
    LeaveRequest toEntity(EmployeeLeaveRequestCreateDto createDto);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    void updateEntity(LeaveRequestUpdateDto updateDto, @MappingTarget LeaveRequest leaveRequest);
}
