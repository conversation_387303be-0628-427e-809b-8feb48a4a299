package com.gianghp.hrm_time_service.specifications;

import com.gianghp.hrm.enums.ApproveStatus;
import com.gianghp.hrm.enums.WorkType;
import com.gianghp.hrm_time_service.dtos.params.AdminOvertimeRequestParams;
import com.gianghp.hrm_time_service.dtos.params.ManagerOvertimeRequestParams;
import com.gianghp.hrm_time_service.entities.OvertimeRequest;
import org.springframework.data.jpa.domain.Specification;

import jakarta.persistence.criteria.Predicate;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class OvertimeRequestSpecification {

    public static Specification<OvertimeRequest> buildSpecification(AdminOvertimeRequestParams params) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // Employee filtering
            if (params.getEmployeeId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("employeeCache").get("id"), params.getEmployeeId()));
            }

            if (params.getEmployeeCode() != null && !params.getEmployeeCode().trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("employeeCache").get("employeeCode")),
                    "%" + params.getEmployeeCode().toLowerCase() + "%"
                ));
            }

            if (params.getEmployeeName() != null && !params.getEmployeeName().trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("employeeCache").get("fullName")),
                    "%" + params.getEmployeeName().toLowerCase() + "%"
                ));
            }

            // Department filtering
            if (params.getDepartmentId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("employeeCache").get("departmentId"), params.getDepartmentId()));
            }

            // Date filtering
            if (params.getDate() != null) {
                predicates.add(criteriaBuilder.equal(root.get("date"), params.getDate()));
            }

            if (params.getStartDate() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("date"), params.getStartDate()));
            }

            if (params.getEndDate() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("date"), params.getEndDate()));
            }

            // Total hours filtering
            if (params.getMinTotalHours() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("totalHours"), params.getMinTotalHours()));
            }

            if (params.getMaxTotalHours() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("totalHours"), params.getMaxTotalHours()));
            }

            // Duration filtering (backward compatibility)
            if (params.getMinDuration() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("totalHours"), params.getMinDuration()));
            }

            if (params.getMaxDuration() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("totalHours"), params.getMaxDuration()));
            }

            // Status filtering
            if (params.getStatus() != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), params.getStatus()));
            }

            // Work type filtering
            if (params.getWorkType() != null) {
                predicates.add(criteriaBuilder.equal(root.get("employeeCache").get("workType"), params.getWorkType()));
            }



            // Overtime setting filtering
            if (params.getOvertimeSettingId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("overtimeSetting").get("id"), params.getOvertimeSettingId()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    // Individual specification methods for reusability
    public static Specification<OvertimeRequest> hasEmployeeId(UUID employeeId) {
        return (root, query, criteriaBuilder) -> 
            employeeId == null ? null : criteriaBuilder.equal(root.get("employeeCache").get("id"), employeeId);
    }

    public static Specification<OvertimeRequest> hasStatus(ApproveStatus status) {
        return (root, query, criteriaBuilder) -> 
            status == null ? null : criteriaBuilder.equal(root.get("status"), status);
    }

    public static Specification<OvertimeRequest> hasDate(LocalDate date) {
        return (root, query, criteriaBuilder) -> 
            date == null ? null : criteriaBuilder.equal(root.get("date"), date);
    }

    public static Specification<OvertimeRequest> hasDateRange(LocalDate startDate, LocalDate endDate) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            if (startDate != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("date"), startDate));
            }
            
            if (endDate != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("date"), endDate));
            }
            
            return predicates.isEmpty() ? null : criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<OvertimeRequest> hasWorkType(WorkType workType) {
        return (root, query, criteriaBuilder) -> 
            workType == null ? null : criteriaBuilder.equal(root.get("employeeCache").get("workType"), workType);
    }

    public static Specification<OvertimeRequest> hasOvertimeSettingId(UUID overtimeSettingId) {
        return (root, query, criteriaBuilder) -> 
            overtimeSettingId == null ? null : criteriaBuilder.equal(root.get("overtimeSetting").get("id"), overtimeSettingId);
    }

    public static Specification<OvertimeRequest> hasTotalHoursRange(Double minHours, Double maxHours) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (minHours != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("totalHours"), minHours));
            }

            if (maxHours != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("totalHours"), maxHours));
            }

            return predicates.isEmpty() ? null : criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
