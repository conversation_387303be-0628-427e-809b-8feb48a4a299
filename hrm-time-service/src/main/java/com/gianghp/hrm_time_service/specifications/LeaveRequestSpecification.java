package com.gianghp.hrm_time_service.specifications;

import com.gianghp.hrm.enums.LeaveRequestStatus;
import com.gianghp.hrm.enums.WorkType;
import com.gianghp.hrm_time_service.dtos.params.AdminLeaveRequestParams;
import com.gianghp.hrm_time_service.dtos.params.ManagerLeaveRequestParams;
import com.gianghp.hrm_time_service.entities.LeaveRequest;
import org.springframework.data.jpa.domain.Specification;

import jakarta.persistence.criteria.Predicate;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class LeaveRequestSpecification {

    public static Specification<LeaveRequest> buildSpecification(AdminLeaveRequestParams params) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // Employee filtering
            if (params.getEmployeeId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("employeeCache").get("id"), params.getEmployeeId()));
            }

            if (params.getEmployeeCode() != null && !params.getEmployeeCode().trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("employeeCache").get("employeeCode")),
                    "%" + params.getEmployeeCode().toLowerCase() + "%"
                ));
            }

            if (params.getEmployeeName() != null && !params.getEmployeeName().trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("employeeCache").get("fullName")),
                    "%" + params.getEmployeeName().toLowerCase() + "%"
                ));
            }

            // Department filtering
            if (params.getDepartmentId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("employeeCache").get("departmentId"), params.getDepartmentId()));
            }


            // Date filtering
            if (params.getStartDate() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("startDate"), params.getStartDate()));
            }

            if (params.getEndDate() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("endDate"), params.getEndDate()));
            }

            // Status filtering
            if (params.getStatus() != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), params.getStatus()));
            }

            // Leave type filtering
            if (params.getLeaveTypeId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("leaveType").get("id"), params.getLeaveTypeId()));
            }

            if (params.getLeaveTypeName() != null && !params.getLeaveTypeName().trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("leaveType").get("name")),
                    "%" + params.getLeaveTypeName().toLowerCase() + "%"
                ));
            }

            // Duration filtering
            if (params.getMinDays() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("numberOfDays"), params.getMinDays()));
            }

            if (params.getMaxDays() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("numberOfDays"), params.getMaxDays()));
            }

            // Work type filtering
            if (params.getWorkType() != null) {
                predicates.add(criteriaBuilder.equal(root.get("employeeCache").get("workType"), params.getWorkType()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    // Individual specification methods for reusability
    public static Specification<LeaveRequest> hasEmployeeId(UUID employeeId) {
        return (root, query, criteriaBuilder) -> 
            employeeId == null ? null : criteriaBuilder.equal(root.get("employeeCache").get("id"), employeeId);
    }

    public static Specification<LeaveRequest> hasStatus(LeaveRequestStatus status) {
        return (root, query, criteriaBuilder) -> 
            status == null ? null : criteriaBuilder.equal(root.get("status"), status);
    }

    public static Specification<LeaveRequest> hasDateRange(LocalDate startDate, LocalDate endDate) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            if (startDate != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("startDate"), startDate));
            }
            
            if (endDate != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("endDate"), endDate));
            }
            
            return predicates.isEmpty() ? null : criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<LeaveRequest> hasLeaveTypeId(UUID leaveTypeId) {
        return (root, query, criteriaBuilder) -> 
            leaveTypeId == null ? null : criteriaBuilder.equal(root.get("leaveType").get("id"), leaveTypeId);
    }

    public static Specification<LeaveRequest> hasWorkType(WorkType workType) {
        return (root, query, criteriaBuilder) -> 
            workType == null ? null : criteriaBuilder.equal(root.get("employeeCache").get("workType"), workType);
    }
}
