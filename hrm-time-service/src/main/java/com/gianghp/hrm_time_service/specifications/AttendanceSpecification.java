package com.gianghp.hrm_time_service.specifications;

import com.gianghp.hrm.enums.CheckinStatus;
import com.gianghp.hrm.enums.CheckoutStatus;
import com.gianghp.hrm.enums.WorkType;
import com.gianghp.hrm_time_service.dtos.params.AdminAttendanceRequestParams;
import com.gianghp.hrm_time_service.dtos.params.MyAttendanceRequestParams;
import com.gianghp.hrm_time_service.entities.Attendance;
import org.springframework.data.jpa.domain.Specification;

import jakarta.persistence.criteria.Predicate;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class AttendanceSpecification {

    public static Specification<Attendance> buildSpecification(AdminAttendanceRequestParams params) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // Employee filtering
            if (params.getEmployeeId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("employeeCache").get("id"), params.getEmployeeId()));
            }

            if (params.getEmployeeCode() != null && !params.getEmployeeCode().trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("employeeCache").get("employeeCode")),
                    "%" + params.getEmployeeCode().toLowerCase() + "%"
                ));
            }

            if (params.getEmployeeName() != null && !params.getEmployeeName().trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("employeeCache").get("fullName")),
                    "%" + params.getEmployeeName().toLowerCase() + "%"
                ));
            }

            // Department filtering
            if (params.getDepartmentId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("employeeCache").get("departmentId"), params.getDepartmentId()));
            }

            // Date filtering
            if (params.getDate() != null) {
                predicates.add(criteriaBuilder.equal(root.get("date"), params.getDate()));
            }

            if (params.getStartDate() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("date"), params.getStartDate()));
            }

            if (params.getEndDate() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("date"), params.getEndDate()));
            }


            // Status filtering
            if (params.getCheckinStatus() != null) {
                predicates.add(criteriaBuilder.equal(root.get("checkinStatus"), params.getCheckinStatus()));
            }

            if (params.getCheckoutStatus() != null) {
                predicates.add(criteriaBuilder.equal(root.get("checkoutStatus"), params.getCheckoutStatus()));
            }

            // Work type filtering
            if (params.getWorkType() != null) {
                predicates.add(criteriaBuilder.equal(root.get("employeeCache").get("workType"), params.getWorkType()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<Attendance> buildSpecification(MyAttendanceRequestParams params) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // Date filtering
            if (params.getDate() != null) {
                predicates.add(criteriaBuilder.equal(root.get("date"), params.getDate()));
            }

            if (params.getStartDate() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("date"), params.getStartDate()));
            }

            if (params.getEndDate() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("date"), params.getEndDate()));
            }

            // Status filtering
            if (params.getCheckinStatus() != null) {
                predicates.add(criteriaBuilder.equal(root.get("checkinStatus"), params.getCheckinStatus()));
            }

            if (params.getCheckoutStatus() != null) {
                predicates.add(criteriaBuilder.equal(root.get("checkoutStatus"), params.getCheckoutStatus()));
            }

            // Work type filtering
            if (params.getWorkType() != null) {
                predicates.add(criteriaBuilder.equal(root.get("employeeCache").get("workType"), params.getWorkType()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    // Individual specification methods for reusability
    public static Specification<Attendance> hasEmployeeId(UUID employeeId) {
        return (root, query, criteriaBuilder) -> 
            employeeId == null ? null : criteriaBuilder.equal(root.get("employeeCache").get("id"), employeeId);
    }

    public static Specification<Attendance> hasDate(LocalDate date) {
        return (root, query, criteriaBuilder) -> 
            date == null ? null : criteriaBuilder.equal(root.get("date"), date);
    }

    public static Specification<Attendance> hasDateRange(LocalDate startDate, LocalDate endDate) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            if (startDate != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("date"), startDate));
            }
            
            if (endDate != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("date"), endDate));
            }
            
            return predicates.isEmpty() ? null : criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<Attendance> hasCheckinStatus(CheckinStatus checkinStatus) {
        return (root, query, criteriaBuilder) ->
            checkinStatus == null ? null : criteriaBuilder.equal(root.get("checkinStatus"), checkinStatus);
    }

    public static Specification<Attendance> hasCheckoutStatus(CheckoutStatus checkoutStatus) {
        return (root, query, criteriaBuilder) ->
            checkoutStatus == null ? null : criteriaBuilder.equal(root.get("checkoutStatus"), checkoutStatus);
    }

    public static Specification<Attendance> hasWorkType(WorkType workType) {
        return (root, query, criteriaBuilder) -> 
            workType == null ? null : criteriaBuilder.equal(root.get("employeeCache").get("workType"), workType);
    }

    public static Specification<Attendance> hasCheckInTimeRange(LocalTime startTime, LocalTime endTime) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            if (startTime != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("checkIn"), startTime));
            }
            
            if (endTime != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("checkIn"), endTime));
            }
            
            return predicates.isEmpty() ? null : criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
