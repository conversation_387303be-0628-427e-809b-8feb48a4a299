package com.gianghp.hrm_time_service.dtos.get;

import com.gianghp.hrm.enums.LeaveRequestStatus;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LeaveRequestDto {
    private UUID id;
    private UUID employeeId;
    private String employeeCode;
    private String employeeName;
    private UUID leaveTypeId;
    private String leaveTypeName;
    private LocalDate startDate;
    private LocalDate endDate;
    private String reason;
    private int numberOfDays;
    private LeaveRequestStatus status;
    private UUID approverId;
    private String approverName;
    private LocalDate approvedAt;
    private String note;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
