package com.gianghp.hrm_time_service.dtos.params;

import com.gianghp.hrm.enums.LeaveRequestStatus;
import com.gianghp.hrm.enums.WorkType;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.YearMonth;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AdminLeaveRequestParams {
    
    // Employee filtering
    private UUID employeeId;
    private String employeeCode;
    private String employeeName;

    // Department filtering
    private UUID departmentId;
    
    // Date filtering
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate startDate;
    
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate endDate;
    

    private String month;
    
    @DateTimeFormat(pattern = "yyyy")
    private Integer year;
    
    // Status filtering
    private LeaveRequestStatus status;

    // Leave type filtering
    private UUID leaveTypeId;
    private String leaveTypeName;

    // Duration filtering
    private Integer minDays;
    private Integer maxDays;

    // Work type filtering
    private WorkType workType;
    
    // Pagination and sorting
    @Builder.Default
    private int page = 0;
    @Builder.Default
    private int size = 10;
    @Builder.Default
    private String sortBy = "createdAt";
    @Builder.Default
    private String sortDir = "desc";
    
    // Special filters
    private Boolean isApproved;
    private Boolean isPending;
    private Boolean isRejected;
    private Boolean isCurrentMonth;
    private Boolean isCurrentYear;
}
