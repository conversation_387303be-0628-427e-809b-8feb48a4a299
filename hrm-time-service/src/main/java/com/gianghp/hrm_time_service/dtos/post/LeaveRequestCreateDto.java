package com.gianghp.hrm_time_service.dtos.post;

import com.gianghp.hrm.enums.LeaveRequestStatus;
import lombok.*;

import java.time.LocalDate;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LeaveRequestCreateDto {
    private UUID employeeId;
    private UUID leaveTypeId;
    private LocalDate startDate;
    private LocalDate endDate;
    private String reason;
    private LeaveRequestStatus status = LeaveRequestStatus.PENDING;
    private UUID approverId;
    private LocalDate approvedAt;
    private String note;
}
