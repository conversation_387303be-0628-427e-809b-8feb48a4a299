package com.gianghp.hrm_time_service.dtos.get;

import com.gianghp.hrm.enums.WorkType;
import lombok.*;

import java.time.LocalDateTime;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EmployeeCacheDto {
    private UUID id;
    private String employeeCode;
    private String fullName;
    private String departmentName;
    private WorkType workType;
    private LocalDateTime updatedAt;
}
