package com.gianghp.hrm_time_service.dtos.get;

import com.gianghp.hrm.enums.WorkType;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AttendanceSettingDto {
    private UUID id;
    private LocalTime standardCheckIn;
    private LocalTime standardCheckOut;
    private int lateThresholdMinutes;
    private int earlyLeaveThresholdMinutes;
    private int minWorkingMinutes;
    private BigDecimal standardWorkingHours;
    private WorkType workType;
    private LocalDate effectiveDate;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    // Thêm vào cả Entity và DTO
    private LocalTime checkInStart;   // ví dụ: 06:00 → bắt đầu cho phép checkin
    private LocalTime checkInEnd;     // ví dụ: 09:00 → không cho phép checkin sau thời gian này

    private LocalTime checkOutStart;  // ví dụ: 16:00 → bắt đầu cho phép checkout
    private LocalTime checkOutEnd;    // ví dụ: 20:00 → không cho phép checkout sau thời gian này

}
