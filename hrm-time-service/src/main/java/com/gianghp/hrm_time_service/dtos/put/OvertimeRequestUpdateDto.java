package com.gianghp.hrm_time_service.dtos.put;

import com.gianghp.hrm.enums.ApproveStatus;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OvertimeRequestUpdateDto {
    private LocalDate date;
    private BigDecimal totalHours;
    private String reason;
    private UUID approverId;
    private ApproveStatus status;
    private UUID overtimeSettingId;
}
