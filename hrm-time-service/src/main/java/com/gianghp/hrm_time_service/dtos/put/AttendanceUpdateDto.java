package com.gianghp.hrm_time_service.dtos.put;

import com.gianghp.hrm.enums.CheckinStatus;
import com.gianghp.hrm.enums.CheckoutStatus;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AttendanceUpdateDto {
    private UUID employeeId;
    private LocalDate date;
    private LocalTime checkIn;
    private LocalTime checkOut;
    private CheckinStatus checkinStatus;
    private CheckoutStatus checkoutStatus;
    private BigDecimal standardWorkingHours;
    private BigDecimal overtimeHours;
}
