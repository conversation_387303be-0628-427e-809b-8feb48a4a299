package com.gianghp.hrm_time_service.dtos.get;

import com.gianghp.hrm.enums.CheckinStatus;
import com.gianghp.hrm.enums.CheckoutStatus;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AttendanceDto {
    private UUID id;
    private UUID employeeId;
    private String employeeCode;
    private String fullName;
    private LocalDate date;
    private LocalTime checkIn;
    private LocalTime checkOut;
    private CheckinStatus checkinStatus;
    private CheckoutStatus checkoutStatus;
    private BigDecimal standardWorkingHours;
    private BigDecimal overtimeHours;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private boolean autoCheckout;

    // Helper method to calculate total hours
    public BigDecimal getTotalHours() {
        BigDecimal standard = standardWorkingHours != null ? standardWorkingHours : BigDecimal.ZERO;
        BigDecimal overtime = overtimeHours != null ? overtimeHours : BigDecimal.ZERO;
        return standard.add(overtime);
    }
}
