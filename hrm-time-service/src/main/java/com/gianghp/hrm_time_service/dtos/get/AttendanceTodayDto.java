package com.gianghp.hrm_time_service.dtos.get;

import com.gianghp.hrm.enums.CheckinStatus;
import com.gianghp.hrm.enums.CheckoutStatus;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AttendanceTodayDto {
    private boolean isCheckIn;
    private boolean isCheckOut;
    private LocalTime checkInTime;
    private LocalTime checkOutTime; // nullable
    private CheckinStatus checkinStatus;
    private CheckoutStatus checkoutStatus;
}

