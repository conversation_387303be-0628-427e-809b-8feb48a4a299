package com.gianghp.hrm_time_service.dtos.post;

import com.gianghp.hrm.enums.ApproveStatus;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EmployeeOvertimeRequestCreateDto {
    private LocalDate date;
    private BigDecimal totalHours;
    private String reason;
    private ApproveStatus status;
}
