package com.gianghp.hrm_time_service.dtos.post;

import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OvertimeSettingCreateDto {
    private BigDecimal weekdayRate;
    private BigDecimal holidayRate;
    private int minimumOvertimeMinutes;
    private BigDecimal maxHoursPerDay;
    private int maxHoursPerMonth;
    private int maxDayInThePastToRequest;
    private int maxDayInTheFutureToRequest;
    private LocalDate effectiveDate;
}
