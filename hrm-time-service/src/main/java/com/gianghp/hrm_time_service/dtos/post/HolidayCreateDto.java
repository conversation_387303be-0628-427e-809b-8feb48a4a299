package com.gianghp.hrm_time_service.dtos.post;

import com.gianghp.hrm.enums.HolidayType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class HolidayCreateDto {
    private String name;
    private LocalDate date;
    private HolidayType holidayType;
    private String description;
    private boolean isRecurring;
}
