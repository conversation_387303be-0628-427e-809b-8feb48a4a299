package com.gianghp.hrm_time_service.dtos.post;


import com.gianghp.hrm.enums.LeaveRequestStatus;
import com.gianghp.hrm_time_service.entities.LeaveType;
import lombok.*;

import java.time.LocalDate;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class EmployeeLeaveRequestCreateDto {
    private UUID leaveTypeId;
    private String reason;
    private LocalDate startDate;
    private LocalDate endDate;
    private LeaveRequestStatus status = LeaveRequestStatus.PENDING;
}
