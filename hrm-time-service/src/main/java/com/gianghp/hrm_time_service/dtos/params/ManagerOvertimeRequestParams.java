package com.gianghp.hrm_time_service.dtos.params;

import com.gianghp.hrm.enums.ApproveStatus;
import com.gianghp.hrm.enums.WorkType;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.YearMonth;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ManagerOvertimeRequestParams {
    
    // Employee filtering
    private UUID employeeId;
    private String employeeCode;
    private String employeeName;

    
    // Date filtering
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate date;
    
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate startDate;
    
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate endDate;

    private String month;
    
    @DateTimeFormat(pattern = "yyyy")
    private Integer year;
    
    // Total hours filtering (replaced time filtering)
    private Double minTotalHours;
    private Double maxTotalHours;
    
    // Status filtering
    private ApproveStatus status;


    // Work type filtering
    private WorkType workType;
    
    // Duration filtering (in hours)
    private Double minDuration;
    private Double maxDuration;
    
    // Overtime setting filtering
    private UUID overtimeSettingId;
    
    // Pagination and sorting
    @Builder.Default
    private int page = 0;
    @Builder.Default
    private int size = 10;
    @Builder.Default
    private String sortBy = "createdAt";
    @Builder.Default
    private String sortDir = "desc";
    
    // Special filters
    private Boolean isApproved;
    private Boolean isPending;
    private Boolean isRejected;
    private Boolean isCurrentMonth;
    private Boolean isCurrentYear;
    private Boolean isWeekend;
    private Boolean isHoliday;
}
