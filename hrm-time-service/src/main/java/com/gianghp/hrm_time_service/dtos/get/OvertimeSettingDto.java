package com.gianghp.hrm_time_service.dtos.get;

import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OvertimeSettingDto {
    private UUID id;
    private BigDecimal weekdayRate;
    private BigDecimal holidayRate;
    private int minimumOvertimeMinutes;
    private BigDecimal maxHoursPerDay;
    private int maxHoursPerMonth;
    private int maxDayInThePastToRequest;
    private int maxDayInTheFutureToRequest;
    private LocalDate effectiveDate;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
