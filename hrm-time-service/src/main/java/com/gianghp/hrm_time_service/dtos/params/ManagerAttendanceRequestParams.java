package com.gianghp.hrm_time_service.dtos.params;

import com.gianghp.hrm.enums.CheckinStatus;
import com.gianghp.hrm.enums.CheckoutStatus;
import com.gianghp.hrm.enums.WorkType;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.YearMonth;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ManagerAttendanceRequestParams {
    // Employee filtering
    private UUID employeeId;
    private String employeeCode;
    private String employeeName;

    // Date filtering
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate date;

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate startDate;

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate endDate;

    private String month;

    // Status filtering
    private CheckinStatus checkinStatus;
    private CheckoutStatus checkoutStatus;

    // Work type filtering
    private WorkType workType;

    // Pagination and sorting
    @Builder.Default
    private int page = 0;
    @Builder.Default
    private int size = 10;
    @Builder.Default
    private String sortBy = "createdAt";
    @Builder.Default
    private String sortDir = "desc";
}
