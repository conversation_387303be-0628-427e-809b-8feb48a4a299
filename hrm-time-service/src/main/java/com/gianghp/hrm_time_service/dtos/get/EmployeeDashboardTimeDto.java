package com.gianghp.hrm_time_service.dtos.get;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.Month;
import java.util.Map;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeDashboardTimeDto {

    private UUID employeeId;

    private BigDecimal totalWorkingHoursThisWeek;
    private BigDecimal totalWorkingHoursThisMonth;
    private BigDecimal totalOvertimeHoursThisMonth;
    private AttendanceTodayDto attendanceToday;
    private Map<Month, AttendanceMonthDto> attendanceThisYears;
    private LeaveThisMonthDto leaveThisMonth;
    private OvertimeThisMonthDto overtimeThisMonth;

}
