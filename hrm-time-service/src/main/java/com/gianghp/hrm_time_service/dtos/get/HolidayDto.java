package com.gianghp.hrm_time_service.dtos.get;

import com.gianghp.hrm.enums.HolidayType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class HolidayDto {
    private UUID id;
    private String name;
    private LocalDate date;
    private HolidayType holidayType;
    private String description;
    private boolean isRecurring;
}
