package com.gianghp.hrm_time_service.dtos.post;

import com.gianghp.hrm.enums.WorkType;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AttendanceSettingCreateDto {
    private LocalTime standardCheckIn;
    private LocalTime standardCheckOut;
    private int lateThresholdMinutes;
    private int earlyLeaveThresholdMinutes;
    private int minWorkingMinutes;
    private BigDecimal standardWorkingHours;
    private WorkType workType;
    private LocalDate effectiveDate;
    private LocalTime checkInStart;
    private LocalTime checkInEnd;
    private LocalTime checkOutStart;
    private LocalTime checkOutEnd;
    private BigDecimal lateDeductionRate;
    private BigDecimal earlyOutDeductionRate;
    private int minutePerDeduction;
}
