package com.gianghp.hrm_time_service.dtos.post;

import com.gianghp.hrm.enums.ApproveStatus;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OvertimeRequestCreateDto {
    private UUID employeeId;
    private UUID approverId;
    private LocalDate date;
    private BigDecimal totalHours;
    private String reason;
    private ApproveStatus status;
}
