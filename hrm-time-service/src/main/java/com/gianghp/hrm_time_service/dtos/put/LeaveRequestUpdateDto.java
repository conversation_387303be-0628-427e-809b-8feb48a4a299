package com.gianghp.hrm_time_service.dtos.put;

import com.gianghp.hrm.enums.LeaveRequestStatus;
import lombok.*;

import java.time.LocalDate;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LeaveRequestUpdateDto {
    private UUID leaveTypeId;
    private LocalDate startDate;
    private LocalDate endDate;
    private String reason;
    private int numberOfDays;
    private LeaveRequestStatus status;
    private UUID approverId;
    private LocalDate approvedAt;
    private String note;
}
