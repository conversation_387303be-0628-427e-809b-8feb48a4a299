package com.gianghp.hrm_time_service.dtos.put;

import lombok.*;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OvertimeSettingUpdateDto {
    private BigDecimal weekdayRate;
    private BigDecimal holidayRate;
    private int minimumOvertimeMinutes;
    private BigDecimal maxHoursPerDay;
    private int maxHoursPerMonth;
    private int maxDayInThePastToRequest;
    private int maxDayInTheFutureToRequest;
}
