package com.gianghp.hrm_time_service.dtos.get;

import com.gianghp.hrm.enums.ApproveStatus;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OvertimeRequestDto {
    private UUID id;
    private UUID employeeId;
    private String employeeCode;
    private String employeeName;
    private LocalDate date;
    private BigDecimal totalHours;
    private String reason;
    private String approverName;
    private ApproveStatus status;
    private UUID overtimeSettingId;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
