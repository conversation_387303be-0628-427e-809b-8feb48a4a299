package com.gianghp.hrm_time_service.controllers;

import com.gianghp.hrm.dtos.ApiResponse;
import com.gianghp.hrm.security.JwtService;
import com.gianghp.hrm_time_service.dtos.post.EmployeeLeaveRequestCreateDto;
import com.gianghp.hrm_time_service.dtos.post.LeaveRequestCreateDto;
import com.gianghp.hrm_time_service.dtos.get.LeaveRequestDto;
import com.gianghp.hrm_time_service.dtos.get.LeaveThisMonthDto;
import com.gianghp.hrm_time_service.dtos.params.ManagerLeaveRequestParams;
import com.gianghp.hrm_time_service.dtos.params.MyLeaveRequestParams;
import com.gianghp.hrm_time_service.dtos.params.AdminLeaveRequestParams;
import com.gianghp.hrm_time_service.dtos.put.LeaveRequestApproveDto;
import com.gianghp.hrm_time_service.dtos.put.LeaveRequestRejecterDto;
import com.gianghp.hrm_time_service.services.LeaveRequestService;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/leave-requests")
@RequiredArgsConstructor
@Slf4j
public class LeaveRequestController {

    private final LeaveRequestService leaveRequestService;
    private final JwtService jwtService;

    @GetMapping
    public ResponseEntity<ApiResponse<List<LeaveRequestDto>>> getAllLeaveRequests(@ParameterObject AdminLeaveRequestParams params) {
        try {
            Page<LeaveRequestDto> leaveRequestDtos = leaveRequestService.findByParams(params);
            ApiResponse<List<LeaveRequestDto>> response = ApiResponse.success(
                    "Get all leave requests successfully",
                    leaveRequestDtos.getContent(),
                    leaveRequestDtos.getTotalElements(),
                    leaveRequestDtos.getTotalPages(),
                    leaveRequestDtos.getNumber()
            );

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve leave requests: " + e.getMessage()));
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<LeaveRequestDto>> getLeaveRequestById(@PathVariable UUID id) {
        try {
            ApiResponse<LeaveRequestDto> response = ApiResponse.success(
                    "Leave request retrieved successfully",
                    leaveRequestService.findById(id)
            );
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve leave request: " + e.getMessage()));
        }
    }

    @GetMapping("/employee/{employeeId}")
    public ResponseEntity<ApiResponse<List<LeaveRequestDto>>> getLeaveRequestsByEmployee(
            @PathVariable UUID employeeId,
            @ParameterObject AdminLeaveRequestParams params
    ) {
        try {
            // Set the employeeId from path variable
            params.setEmployeeId(employeeId);

            Page<LeaveRequestDto> leaveRequestDtos = leaveRequestService.findByParams(params);

            ApiResponse<List<LeaveRequestDto>> response = ApiResponse.success(
                    "Leave requests retrieved successfully",
                    leaveRequestDtos.getContent(),
                    leaveRequestDtos.getTotalElements(),
                    leaveRequestDtos.getTotalPages(),
                    leaveRequestDtos.getNumber()
            );

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve leave requests: " + e.getMessage()));
        }
    }

    @GetMapping("/employee/{employeeId}/total-leave-this-month")
    public ResponseEntity<ApiResponse<LeaveThisMonthDto>> getLeaveTotalRequestsByEmployee(
            @PathVariable UUID employeeId
    ) {
        try {
            ApiResponse<LeaveThisMonthDto> response = ApiResponse.success(
                    "Leave requests retrieved successfully",
                    leaveRequestService.getLeaveThisMonth(employeeId)
            );
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve leave requests: " + e.getMessage()));
        }
    }

    @GetMapping("/me")
    public ResponseEntity<ApiResponse<List<LeaveRequestDto>>> getMyLeaveRequests(
            @ParameterObject MyLeaveRequestParams params,
            @Parameter(hidden = true) @RequestHeader("Authorization") String authHeader
    ) {
        try {
            // Lấy token và userId
            String token = authHeader.substring(7);
            UUID userId = jwtService.extractUserId(token);


            Page<LeaveRequestDto> leaveRequestDtos = leaveRequestService.findMyByParams(userId, params);

            ApiResponse<List<LeaveRequestDto>> response = ApiResponse.success(
                    "Leave requests retrieved successfully",
                    leaveRequestDtos.getContent(),
                    leaveRequestDtos.getTotalElements(),
                    leaveRequestDtos.getTotalPages(),
                    leaveRequestDtos.getNumber()
            );

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve leave requests: " + e.getMessage()));
        }
    }

    @GetMapping("/me/total-leave-this-month")
    public ResponseEntity<ApiResponse<LeaveThisMonthDto>> getMyTotalLeaveRequests(
            @Parameter(hidden = true) @RequestHeader("Authorization") String authHeader
    ) {
        try {
            String token = authHeader.substring(7);
            UUID userId = jwtService.extractUserId(token);

            ApiResponse<LeaveThisMonthDto> response = ApiResponse.success(
                    "Leave requests retrieved successfully",
                    leaveRequestService.getMyLeaveThisMonth(userId)
            );
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve leave requests: " + e.getMessage()));
        }
    }

    @GetMapping("/manager/employees")
    public ResponseEntity<ApiResponse<List<LeaveRequestDto>>> getManagerEmployeeLeaveRequests(
            @ParameterObject ManagerLeaveRequestParams params,
            @Parameter(hidden = true) @RequestHeader("Authorization") String authHeader
    ) {
        try {
            String token = authHeader.substring(7);
            UUID userId = jwtService.extractUserId(token);


            Page<LeaveRequestDto> leaveRequestDtos = leaveRequestService.findByManagerParams(userId, params);

            ApiResponse<List<LeaveRequestDto>> response = ApiResponse.success(
                    "Leave requests retrieved successfully",
                    leaveRequestDtos.getContent(),
                    leaveRequestDtos.getTotalElements(),
                    leaveRequestDtos.getTotalPages(),
                    leaveRequestDtos.getNumber()
            );

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve leave requests: " + e.getMessage()));
        }
    }


    @PostMapping
    public ResponseEntity<ApiResponse<LeaveRequestDto>> createLeaveRequest(@RequestBody LeaveRequestCreateDto createDto) {
        try {
            ApiResponse<LeaveRequestDto> response = ApiResponse.success(
                    "Leave request created successfully",
                    leaveRequestService.create(createDto)
            );
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to create leave request: " + e.getMessage()));
        }
    }

    @PostMapping("/me")
    public ResponseEntity<ApiResponse<LeaveRequestDto>> createMyLeaveRequest(@RequestBody EmployeeLeaveRequestCreateDto createDto, @Parameter(hidden = true) @RequestHeader("Authorization") String authHeader) {
        try {
            log.info("Creating leave request for employee {}", createDto);
            String token = authHeader.substring(7);
            UUID userId = jwtService.extractUserId(token);
            ApiResponse<LeaveRequestDto> response = ApiResponse.success(
                    "Leave request created successfully",
                    leaveRequestService.createMyLeaveRequest(userId, createDto)
            );
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to create leave request: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}/approve")
    public ResponseEntity<ApiResponse<String>> approveLeaveRequest(
            @PathVariable UUID id,
            @Parameter(hidden = true) @RequestHeader("Authorization") String authHeader
    ) {
        try {
            String token = authHeader.substring(7);
            UUID approverId = jwtService.extractUserId(token);
            leaveRequestService.approve(id, approverId);
            return ResponseEntity.ok(ApiResponse.success("Leave request approved successfully", null));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to approve leave request: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}/reject")
    public ResponseEntity<ApiResponse<String>> rejectLeaveRequest(
            @PathVariable UUID id,
            @Parameter(hidden = true) @RequestHeader("Authorization") String authHeader
    ) {
        try {
            String token = authHeader.substring(7);
            UUID approverId = jwtService.extractUserId(token);
            leaveRequestService.reject(id, approverId);
            return ResponseEntity.ok(ApiResponse.success("Leave request rejected successfully", null));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to approve leave request: " + e.getMessage()));
        }
    }


}
