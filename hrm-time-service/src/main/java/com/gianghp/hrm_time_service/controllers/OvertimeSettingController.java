package com.gianghp.hrm_time_service.controllers;

import com.gianghp.hrm.dtos.ApiResponse;
import com.gianghp.hrm_time_service.dtos.post.OvertimeSettingCreateDto;
import com.gianghp.hrm_time_service.dtos.get.OvertimeSettingDto;
import com.gianghp.hrm_time_service.services.OvertimeSettingService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/overtime-settings")
@RequiredArgsConstructor
public class OvertimeSettingController {
    
    private final OvertimeSettingService overtimeSettingService;
    
    @GetMapping
    public ResponseEntity<ApiResponse<OvertimeSettingDto>> getCurrentOvertimeSetting() {
        try {
            OvertimeSettingDto setting = overtimeSettingService.getCurrentSetting();
            return ResponseEntity.ok(ApiResponse.success("Current overtime setting retrieved successfully", setting));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve current overtime setting: " + e.getMessage()));
        }
    }
    
    @PostMapping
    public ResponseEntity<ApiResponse<OvertimeSettingDto>> createOvertimeSetting(@RequestBody OvertimeSettingCreateDto createDto) {
        try {
            OvertimeSettingDto createdSetting = overtimeSettingService.create(createDto);
            return ResponseEntity.ok(ApiResponse.success("Overtime setting created successfully", createdSetting));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to create overtime setting: " + e.getMessage()));
        }
    }
}
