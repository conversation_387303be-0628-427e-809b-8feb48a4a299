package com.gianghp.hrm_time_service.controllers;

import com.gianghp.hrm.dtos.ApiResponse;
import com.gianghp.hrm.enums.WorkType;
import com.gianghp.hrm_time_service.dtos.post.AttendanceSettingCreateDto;
import com.gianghp.hrm_time_service.dtos.get.AttendanceSettingDto;
import com.gianghp.hrm_time_service.services.AttendanceSettingService;
import com.gianghp.hrm_time_service.services.DynamicSchedulerService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/attendance-settings")
@RequiredArgsConstructor
public class AttendanceSettingController {

    private final AttendanceSettingService attendanceSettingService;
    private final DynamicSchedulerService dynamicSchedulerService;

    @GetMapping
    public ResponseEntity<ApiResponse<AttendanceSettingDto>> getCurrentAttendanceSettings(@RequestParam WorkType workType) {
        try {
            AttendanceSettingDto settings = attendanceSettingService.findCurrentSetting(workType);
            return ResponseEntity.ok(ApiResponse.success("Attendance settings retrieved successfully", settings));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve attendance settings: " + e.getMessage()));
        }
    }


    @PostMapping
    public ResponseEntity<ApiResponse<AttendanceSettingDto>> updateAttendanceSetting(@RequestBody AttendanceSettingCreateDto createDto) {
        try {
            AttendanceSettingDto updatedSetting = attendanceSettingService.create(createDto);
            return ResponseEntity.ok(ApiResponse.success("Attendance setting updated successfully", updatedSetting));

        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to update attendance setting: " + e.getMessage()));
        }
    }

    @GetMapping("/reschedule")
    public ResponseEntity<ApiResponse<String>> reschedule() {
        try {
            dynamicSchedulerService.init();
            return ResponseEntity.ok(ApiResponse.success("Rescheduled successfully", null));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to reschedule: " + e.getMessage()));
        }
    }
}
