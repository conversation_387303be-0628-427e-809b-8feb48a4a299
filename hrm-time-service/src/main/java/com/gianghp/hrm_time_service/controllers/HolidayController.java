package com.gianghp.hrm_time_service.controllers;

import com.gianghp.hrm.dtos.ApiResponse;
import com.gianghp.hrm_time_service.dtos.post.HolidayCreateDto;
import com.gianghp.hrm_time_service.dtos.get.HolidayDto;
import com.gianghp.hrm_time_service.services.HolidayService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/holidays")
@RequiredArgsConstructor
public class HolidayController {
    private final HolidayService holidayService;

    @GetMapping
    public ResponseEntity<ApiResponse<List<HolidayDto>>> getAllHolidays() {
        try {
            List<HolidayDto> holidays = holidayService.getAll();
            return ResponseEntity.ok(ApiResponse.success("Holidays retrieved successfully", holidays));
        }
        catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve holidays: " + e.getMessage()));
        }
    }

    @PostMapping
    public ResponseEntity<ApiResponse<HolidayDto>> createHoliday(@RequestBody HolidayCreateDto createDto) {
        try {
            HolidayDto holidayDto = holidayService.createHoliday(createDto);
            return ResponseEntity.ok(ApiResponse.success("Holiday created successfully", holidayDto));
        }
        catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to create holiday: " + e.getMessage()));
        }
    }
}
