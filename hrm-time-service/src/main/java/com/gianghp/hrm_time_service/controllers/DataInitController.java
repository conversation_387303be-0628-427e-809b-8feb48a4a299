package com.gianghp.hrm_time_service.controllers;

import com.gianghp.hrm_time_service.services.DataInitializer;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/data-init")
@RequiredArgsConstructor
public class DataInitController {
    private final DataInitializer dataInitializer;

    @GetMapping
    public void init() {
        dataInitializer.init();
    }
}
