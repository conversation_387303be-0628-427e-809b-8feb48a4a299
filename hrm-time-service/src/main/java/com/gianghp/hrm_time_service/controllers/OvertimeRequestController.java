package com.gianghp.hrm_time_service.controllers;

import com.gianghp.hrm.dtos.ApiResponse;
import com.gianghp.hrm.security.JwtService;
import com.gianghp.hrm_time_service.dtos.post.EmployeeOvertimeRequestCreateDto;
import com.gianghp.hrm_time_service.dtos.post.OvertimeRequestCreateDto;
import com.gianghp.hrm_time_service.dtos.get.OvertimeRequestDto;
import com.gianghp.hrm_time_service.dtos.get.OvertimeSummaryThisMonth;
import com.gianghp.hrm_time_service.dtos.params.ManagerOvertimeRequestParams;
import com.gianghp.hrm_time_service.dtos.params.MyOvertimeRequestParams;
import com.gianghp.hrm_time_service.dtos.params.AdminOvertimeRequestParams;
import com.gianghp.hrm_time_service.services.OvertimeRequestService;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/overtime-requests")
@RequiredArgsConstructor
public class OvertimeRequestController {

    private final OvertimeRequestService overtimeRequestService;
    private final JwtService jwtService;

    @GetMapping
    public ResponseEntity<ApiResponse<List<OvertimeRequestDto>>> getAllOvertimeRequests(@ParameterObject AdminOvertimeRequestParams params) {
        try {
            Page<OvertimeRequestDto> overtimeRequestDtos = overtimeRequestService.findByParams(params);

            ApiResponse<List<OvertimeRequestDto>> response = ApiResponse.success(
                    "Get all overtime requests successfully",
                    overtimeRequestDtos.getContent(),
                    overtimeRequestDtos.getTotalElements(),
                    overtimeRequestDtos.getTotalPages(),
                    overtimeRequestDtos.getNumber()
            );
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve overtime requests: " + e.getMessage()));
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<OvertimeRequestDto>> getOvertimeRequestById(@PathVariable UUID id) {
        try {
            OvertimeRequestDto overtimeRequestDto = overtimeRequestService.findById(id);
            ApiResponse<OvertimeRequestDto> response = ApiResponse.success(
                    "Overtime request retrieved successfully",
                    overtimeRequestDto
            );
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve overtime request: " + e.getMessage()));
        }
    }

    @GetMapping("/employee/{employeeId}")
    public ResponseEntity<ApiResponse<List<OvertimeRequestDto>>> getOvertimeRequestsByEmployee(
            @PathVariable UUID employeeId,
            @ParameterObject AdminOvertimeRequestParams params
    ) {
        try {
            // Set the employeeId from path variable
            params.setEmployeeId(employeeId);

            Page<OvertimeRequestDto> overtimeRequestDtos = overtimeRequestService.findByParams(params);

            ApiResponse<List<OvertimeRequestDto>> response = ApiResponse.success(
                    "Overtime requests retrieved successfully",
                    overtimeRequestDtos.getContent(),
                    overtimeRequestDtos.getTotalElements(),
                    overtimeRequestDtos.getTotalPages(),
                    overtimeRequestDtos.getNumber()
            );

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve overtime requests: " + e.getMessage()));
        }
    }

    @GetMapping("/employee/{employeeId}/overtime-summary-this-month")
    public ResponseEntity<ApiResponse<OvertimeSummaryThisMonth>> getOvertimeRequestsSummaryByEmployee(
            @PathVariable UUID employeeId
    ) {
        try {
            ApiResponse<OvertimeSummaryThisMonth> response = ApiResponse.success(
                    "Overtime requests retrieved successfully",
                    overtimeRequestService.getOvertimeSummaryThisMonth(employeeId)
            );
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve overtime requests: " + e.getMessage()));
        }
    }

    @GetMapping("/me")
    public ResponseEntity<ApiResponse<List<OvertimeRequestDto>>> getMyOvertimeRequests(
            @ParameterObject MyOvertimeRequestParams params,
            @Parameter(hidden = true) @RequestHeader("Authorization") String authHeader
    ) {
        try {
            // Set the employeeId from path variable
            String token = authHeader.substring(7);
            UUID userId = jwtService.extractUserId(token);


            Page<OvertimeRequestDto> overtimeRequestDtos = overtimeRequestService.findMyByParams(userId, params);

            ApiResponse<List<OvertimeRequestDto>> response = ApiResponse.success(
                    "Overtime requests retrieved successfully",
                    overtimeRequestDtos.getContent(),
                    overtimeRequestDtos.getTotalElements(),
                    overtimeRequestDtos.getTotalPages(),
                    overtimeRequestDtos.getNumber()
            );

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve overtime requests: " + e.getMessage()));
        }
    }

    @GetMapping("/me/overtime-summary-this-month")
    public ResponseEntity<ApiResponse<OvertimeSummaryThisMonth>> getMyOvertimeSummary(
            @Parameter(hidden = true) @RequestHeader("Authorization") String authHeader
    ) {
        try {
            String token = authHeader.substring(7);
            UUID userId = jwtService.extractUserId(token);
            ApiResponse<OvertimeSummaryThisMonth> response = ApiResponse.success(
                    "Overtime requests retrieved successfully",
                    overtimeRequestService.getMyOvertimeSummaryThisMonth(userId)
            );
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve overtime requests: " + e.getMessage()));
        }
    }

    @GetMapping("/manager/employees")
    public ResponseEntity<ApiResponse<List<OvertimeRequestDto>>> getManagerEmployeeOvertimeRequests(
            @ParameterObject ManagerOvertimeRequestParams params,
            @Parameter(hidden = true) @RequestHeader("Authorization") String authHeader
    ) {
        try {
            String token = authHeader.substring(7);
            UUID userId = jwtService.extractUserId(token);


            Page<OvertimeRequestDto> overtimeRequestDtos = overtimeRequestService.findByManagerParams(userId, params);

            ApiResponse<List<OvertimeRequestDto>> response = ApiResponse.success(
                    "Overtime requests retrieved successfully",
                    overtimeRequestDtos.getContent(),
                    overtimeRequestDtos.getTotalElements(),
                    overtimeRequestDtos.getTotalPages(),
                    overtimeRequestDtos.getNumber()
            );

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve overtime requests: " + e.getMessage()));
        }
    }


    @PostMapping
    public ResponseEntity<ApiResponse<OvertimeRequestDto>> createOvertimeRequest(@RequestBody OvertimeRequestCreateDto createDto) {
        try {
            OvertimeRequestDto overtimeRequestDto = overtimeRequestService.create(createDto);
            ApiResponse<OvertimeRequestDto> response = ApiResponse.success(
                    "Overtime request created successfully",
                    overtimeRequestDto
            );
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to create overtime request: " + e.getMessage()));
        }
    }

    @PostMapping("/me")
    public ResponseEntity<ApiResponse<OvertimeRequestDto>> createMyOvertimeRequest(
            @RequestBody EmployeeOvertimeRequestCreateDto createDto,
            @Parameter(hidden = true) @RequestHeader("Authorization") String authHeader
    ) {
        try {
            String token = authHeader.substring(7);
            UUID userId = jwtService.extractUserId(token);
            OvertimeRequestDto overtimeRequestDto = overtimeRequestService.createMyOvertimeRequest(userId, createDto);
            ApiResponse<OvertimeRequestDto> response = ApiResponse.success(
                    "Overtime request created successfully",
                    overtimeRequestDto
            );
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to create overtime request: " + e.getMessage()));

        }

    }


    @PutMapping("/{id}/approve")
    public ResponseEntity<ApiResponse<String>> approveOvertimeRequest(
            @PathVariable UUID id,
            @Parameter(hidden = true) @RequestHeader("Authorization") String authHeader
    ) {
        try {
            String token = authHeader.substring(7);
            UUID approverId = jwtService.extractUserId(token);

            overtimeRequestService.approve(
                    id,
                    approverId
            );
            return ResponseEntity.ok(ApiResponse.success("Overtime request approved successfully", null));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to approve overtime request: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}/reject")
    public ResponseEntity<ApiResponse<String>> rejectOvertimeRequest(@PathVariable UUID id, @Parameter(hidden = true) @RequestHeader("Authorization") String authHeader) {
        try {
            String token = authHeader.substring(7);
            UUID approverId = jwtService.extractUserId(token);
            overtimeRequestService.reject(
                    id,
                    approverId
            );
            return ResponseEntity.ok(ApiResponse.success("Overtime request rejected successfully", null));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to reject overtime request: " + e.getMessage()));
        }
    }
}
