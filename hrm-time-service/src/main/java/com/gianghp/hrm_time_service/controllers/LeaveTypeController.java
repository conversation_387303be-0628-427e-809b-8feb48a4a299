package com.gianghp.hrm_time_service.controllers;

import com.gianghp.hrm.dtos.ApiResponse;
import com.gianghp.hrm_time_service.dtos.get.LeaveTypeDto;
import com.gianghp.hrm_time_service.services.LeaveTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/leave-types")
@RequiredArgsConstructor
public class LeaveTypeController {
    
    private final LeaveTypeService leaveTypeService;
    
    @GetMapping
    public ResponseEntity<ApiResponse<List<LeaveTypeDto>>> getAllLeaveTypes() {
        try {
            List<LeaveTypeDto> leaveTypes = leaveTypeService.findAll();
            return ResponseEntity.ok(ApiResponse.success("Leave types retrieved successfully", leaveTypes));
        }
        catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve leave types: " + e.getMessage()));
        }
    }

    @GetMapping("/active")
    public ResponseEntity<ApiResponse<List<LeaveTypeDto>>> getActiveLeaveTypes() {
        try {
               List<LeaveTypeDto> leaveTypes = leaveTypeService.findActiveLeaveTypes();
               return ResponseEntity.ok(ApiResponse.success("Leave types retrieved successfully", leaveTypes));
        }
        catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve leave types: " + e.getMessage()));
        }
    }

    @GetMapping("/inactive")
    public ResponseEntity<ApiResponse<List<LeaveTypeDto>>> getInactiveLeaveTypes() {
        try {
            List<LeaveTypeDto> leaveTypes = leaveTypeService.findInactiveLeaveTypes();
            return ResponseEntity.ok(ApiResponse.success("Leave types retrieved successfully", leaveTypes));
        }
        catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to retrieve leave types: " + e.getMessage()));
        }
    }


    @PutMapping("/activate")
    public ResponseEntity<ApiResponse<String>> activateLeaveTypes(
            @RequestBody List<UUID> idList
    ) {
        try {
            leaveTypeService.activateBatch(idList);
            return ResponseEntity.ok(ApiResponse.success("Leave types activated successfully", null));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to activate leave types: " + e.getMessage()));
        }
    }

    @PutMapping("/deactivate")
    public ResponseEntity<ApiResponse<String>> deactivateLeaveTypes(
            @RequestBody List<UUID> idList
    ) {
        try {
            leaveTypeService.deactivateBatch(idList);
            return ResponseEntity.ok(ApiResponse.success("Leave types deactivated successfully", null));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Failed to deactivate leave types: " + e.getMessage()));
        }
    }
}
