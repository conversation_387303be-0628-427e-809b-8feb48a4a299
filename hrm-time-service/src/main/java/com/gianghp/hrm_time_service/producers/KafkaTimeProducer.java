package com.gianghp.hrm_time_service.producers;

import com.gianghp.hrm.constants.TopicConstants;
import com.gianghp.hrm.dtos.ApprovedOvertimeBasicDto;
import com.gianghp.hrm.dtos.AttendanceBasicDto;
import com.gianghp.hrm.dtos.AttendanceBasicWrapperDto;
import com.gianghp.hrm.dtos.AttendanceSettingBasicDto;
import com.gianghp.hrm.dtos.EmployeeBasicDto;
import com.gianghp.hrm.dtos.EmployeeCreateFailedDto;
import com.gianghp.hrm_time_service.dtos.kafka.EmployeeUpdateRole;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
@Slf4j
@RequiredArgsConstructor
public class KafkaTimeProducer {
    private final KafkaTemplate<String, EmployeeCreateFailedDto> kafkaCreateFailedTemplate;
    private final KafkaTemplate<String, EmployeeUpdateRole> kafkaUpdateRoleTemplate;
    private final KafkaTemplate<String, ApprovedOvertimeBasicDto> kafkaApprovedOvertimeTemplate;
    private final KafkaTemplate<String, AttendanceBasicWrapperDto> kafkaAttendanceTemplate;

    public void sendEmployeeWhenCreatedFailed(EmployeeBasicDto employee) {
        EmployeeCreateFailedDto employeeCreateFailedDto = new EmployeeCreateFailedDto();
        employeeCreateFailedDto.setEmployeeId(employee.getId());
        employeeCreateFailedDto.setUserId(employee.getUserId());
        Message<EmployeeCreateFailedDto> msg = MessageBuilder.withPayload(
                employeeCreateFailedDto
        ).setHeader(KafkaHeaders.TOPIC, TopicConstants.EMPLOYEE_CREATE_FAILED).build();
        log.info("Sent to {}: {}", TopicConstants.EMPLOYEE_CREATE_FAILED, msg);
        kafkaCreateFailedTemplate.send(msg);
    }


    public void sendWhenUpdateEmployeeRole(EmployeeUpdateRole employeeUpdateRole) {
        Message<EmployeeUpdateRole> msg = MessageBuilder.withPayload(
                employeeUpdateRole
        ).setHeader(KafkaHeaders.TOPIC, TopicConstants.UPDATE_EMPLOYEE_ROLE).build();
        log.info("Sent to {}: {}", TopicConstants.UPDATE_EMPLOYEE_ROLE, msg);
        kafkaUpdateRoleTemplate.send(msg);
    }

    public void sendWhenOvertimeApproved(ApprovedOvertimeBasicDto approvedOvertimeBasicDto) {
        Message<ApprovedOvertimeBasicDto> msg = MessageBuilder.withPayload(
                approvedOvertimeBasicDto
        ).setHeader(KafkaHeaders.TOPIC, TopicConstants.OVERTIME_APPROVED).build();
        log.info("Sent to {}: {}", TopicConstants.OVERTIME_APPROVED, msg);
        kafkaApprovedOvertimeTemplate.send(msg);
    }

    public void sendWhenTodayWorkingHoursCalculated(AttendanceBasicWrapperDto attendanceBasicDtos) {
        Message<AttendanceBasicWrapperDto> msg = MessageBuilder.withPayload(
                attendanceBasicDtos
        ).setHeader(KafkaHeaders.TOPIC, TopicConstants.FINAL_DAILY_ATTENDANCE).build();
        log.info("Sent to {}: {}", TopicConstants.FINAL_DAILY_ATTENDANCE, msg);
        kafkaAttendanceTemplate.send(msg);
    }

    public void sendWhenCreateNewAttendanceSetting(
        AttendanceSettingBasicDto attendanceSettingBasicDto) {
        Message<AttendanceSettingBasicDto> msg = MessageBuilder.withPayload(
                attendanceSettingBasicDto
        ).setHeader(KafkaHeaders.TOPIC, TopicConstants.ATTENDANCE_SETTING_CREATED).build();
        log.info("Sent to {}: {}", TopicConstants.ATTENDANCE_SETTING_CREATED, msg);
        kafkaAttendanceTemplate.send(msg);
    }

}
