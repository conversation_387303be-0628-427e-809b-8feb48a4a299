package com.gianghp.hrm_time_service.consumers;

import com.gianghp.hrm.constants.TopicConstants;
import com.gianghp.hrm.dtos.EmployeeBasicDto;
import com.gianghp.hrm_time_service.acl.EmployeeACL;
import com.gianghp.hrm_time_service.entities.EmployeeCache;
import com.gianghp.hrm_time_service.mappers.EmployeeCacheMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class KafkaTimeConsumer {

    private final EmployeeACL employeeACL;

    @KafkaListener(topics = TopicConstants.EMPLOYEE_CREATED)
    public void consumeEmployeeCreated(EmployeeBasicDto message) {
        log.info("Received from {}: {}", TopicConstants.EMPLOYEE_CREATED, message);
        employeeACL.handleEmployeeCreatedEvent(message);
    }

}
