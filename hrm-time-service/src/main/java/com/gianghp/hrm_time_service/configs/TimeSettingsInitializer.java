package com.gianghp.hrm_time_service.configs;

import com.gianghp.hrm.enums.WorkType;
import com.gianghp.hrm_time_service.dtos.post.AttendanceSettingCreateDto;
import com.gianghp.hrm_time_service.entities.AttendanceSetting;
import com.gianghp.hrm_time_service.entities.LeaveType;
import com.gianghp.hrm_time_service.entities.OvertimeSetting;
import com.gianghp.hrm_time_service.repositories.AttendanceSettingRepository;
import com.gianghp.hrm_time_service.repositories.LeaveTypeRepository;
import com.gianghp.hrm_time_service.repositories.OvertimeSettingRepository;
import com.gianghp.hrm_time_service.services.AttendanceSettingService;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
public class TimeSettingsInitializer {

  private final LeaveTypeRepository leaveTypeRepository;
  private final AttendanceSettingService attendanceSettingService;
  private final OvertimeSettingRepository overtimeSettingRepository;
  private final AttendanceSettingRepository attendanceSettingRepository;

  @PostConstruct
  public void init() {
    log.info("Initializing time-related system settings...");

    initLeaveTypes();
    initAttendanceSettings();
    initOvertimeSettings();

    log.info("Time-related system settings initialized.");
  }

  private void initLeaveTypes() {
    if (leaveTypeRepository.count() > 0) return;

    log.info("Creating default leave types...");
    List<LeaveType> leaveTypes = List.of(
        new LeaveType("Annual Leave", true),
        new LeaveType("Sick Leave", true),
        new LeaveType("Personal Leave", true),
        new LeaveType("Maternity Leave", true),
        new LeaveType("Paternity Leave", true),
        new LeaveType("Emergency Leave", true),
        new LeaveType("Bereavement Leave", true),
        new LeaveType("Study Leave", true),
        new LeaveType("Compensatory Leave", true),
        new LeaveType("Unpaid Leave", true)
    );
    leaveTypeRepository.saveAll(leaveTypes);
  }

  private void initAttendanceSettings() {
    if (attendanceSettingRepository.count() > 0) return;

    log.info("Creating default attendance settings...");

    AttendanceSettingCreateDto fullTime = new AttendanceSettingCreateDto();
    fullTime.setStandardCheckIn(LocalTime.of(8, 0));
    fullTime.setStandardCheckOut(LocalTime.of(17, 30));
    fullTime.setLateThresholdMinutes(15);
    fullTime.setEarlyLeaveThresholdMinutes(15);
    fullTime.setMinWorkingMinutes(240);
    fullTime.setStandardWorkingHours(new BigDecimal("8.5"));
    fullTime.setWorkType(WorkType.FULL_TIME);
    fullTime.setEffectiveDate(LocalDate.of(2024, 1, 1));
    fullTime.setCheckInStart(LocalTime.of(6, 0));
    fullTime.setCheckInEnd(LocalTime.of(9, 0));
    fullTime.setCheckOutStart(LocalTime.of(16, 0));
    fullTime.setCheckOutEnd(LocalTime.of(20, 0));
    fullTime.setLateDeductionRate(new BigDecimal("0.1"));
    fullTime.setEarlyOutDeductionRate(new BigDecimal("0.1"));
    fullTime.setMinutePerDeduction(15);

    AttendanceSettingCreateDto partTime = new AttendanceSettingCreateDto();
    partTime.setStandardCheckIn(LocalTime.of(9, 0));
    partTime.setStandardCheckOut(LocalTime.of(13, 0));
    partTime.setLateThresholdMinutes(10);
    partTime.setEarlyLeaveThresholdMinutes(10);
    partTime.setMinWorkingMinutes(180);
    partTime.setStandardWorkingHours(new BigDecimal("4.0"));
    partTime.setWorkType(WorkType.PART_TIME);
    partTime.setEffectiveDate(LocalDate.of(2024, 1, 1));
    partTime.setCheckInStart(LocalTime.of(7, 0));
    partTime.setCheckInEnd(LocalTime.of(10, 0));
    partTime.setCheckOutStart(LocalTime.of(14, 0));
    partTime.setCheckOutEnd(LocalTime.of(15, 0));
    partTime.setLateDeductionRate(new BigDecimal("0.1"));
    partTime.setEarlyOutDeductionRate(new BigDecimal("0.1"));
    partTime.setMinutePerDeduction(10);

    attendanceSettingService.create(fullTime);
    attendanceSettingService.create(partTime);
  }

  private void initOvertimeSettings() {
    if (overtimeSettingRepository.count() > 0) return;

    log.info("Creating default overtime settings...");

    OvertimeSetting setting = new OvertimeSetting();
    setting.setWeekdayRate(new BigDecimal("1.5"));
    setting.setHolidayRate(new BigDecimal("2.5"));
    setting.setMinimumOvertimeMinutes(30);
    setting.setMaxHoursPerDay(new BigDecimal("4.0"));
    setting.setMaxHoursPerMonth(60);
    setting.setMaxDayInThePastToRequest(7);
    setting.setMaxDayInTheFutureToRequest(30);
    setting.setEffectiveDate(LocalDate.of(2024, 1, 1));

    overtimeSettingRepository.save(setting);
  }
}
