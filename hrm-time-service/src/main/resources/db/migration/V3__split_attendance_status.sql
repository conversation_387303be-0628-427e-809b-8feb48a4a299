-- Migration to split attendance status into checkin_status and checkout_status

-- Add new columns
ALTER TABLE attendance ADD COLUMN checkin_status VARCHAR(20);
ALTER TABLE attendance ADD COLUMN checkout_status VARCHAR(20);

-- Migrate existing data based on current status
UPDATE attendance 
SET checkin_status = CASE 
    WHEN status = 'ON_TIME' THEN 'ON_TIME'
    WHEN status = 'LATE' THEN 'LATE'
    WHEN status = 'ABSENT' THEN 'ABSENT'
    WHEN status = 'UNEXPLAINED_ABSENCE' THEN 'UNEXPLAINED_ABSENCE'
    WHEN status = 'APPROVED_LEAVE' THEN 'APPROVED_LEAVE'
    WHEN status = 'HOLIDAY' THEN 'HOLIDAY'
    WHEN status = 'VACATION' THEN 'VACATION'
    WHEN status = 'SICK_LEAVE' THEN 'SICK_LEAVE'
    ELSE 'ON_TIME'
END;

UPDATE attendance 
SET checkout_status = CASE 
    WHEN status = 'EARLY_OUT' THEN 'EARLY_OUT'
    WHEN status = 'FORGOT_CHECK_OUT' THEN 'FORGOT_CHECK_OUT'
    WHEN status = 'ABSENT' THEN 'ABSENT'
    WHEN status = 'UNEXPLAINED_ABSENCE' THEN 'ABSENT'
    WHEN status = 'APPROVED_LEAVE' THEN 'APPROVED_LEAVE'
    WHEN status = 'HOLIDAY' THEN 'APPROVED_LEAVE'
    WHEN status = 'VACATION' THEN 'APPROVED_LEAVE'
    WHEN status = 'SICK_LEAVE' THEN 'APPROVED_LEAVE'
    WHEN check_out IS NULL THEN 'NOT_CHECKED_OUT'
    ELSE 'ON_TIME'
END;

-- Make checkin_status NOT NULL
ALTER TABLE attendance ALTER COLUMN checkin_status SET NOT NULL;

-- Drop old status column
ALTER TABLE attendance DROP COLUMN status;
