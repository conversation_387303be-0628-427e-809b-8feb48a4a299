package com.gianghp.hrm_auth_service.producers;

import com.gianghp.hrm.constants.TopicConstants;
import com.gianghp.hrm.dtos.EmployeeBasicDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class KafkaUserProducer {

    private final KafkaTemplate<String, EmployeeBasicDto> kafkaTemplate;

    public void sendEmployeeWhenUserCreated(EmployeeBasicDto employee) {
        Message<EmployeeBasicDto> msg = MessageBuilder.withPayload(
                employee
        ).setHeader(KafkaHeaders.TOPIC, TopicConstants.USER_WITH_EMPLOYEE_CREATED).build();
        log.info("Sent to {}: {}", TopicConstants.USER_WITH_EMPLOYEE_CREATED, employee);
        kafkaTemplate.send(msg);
    }
}
