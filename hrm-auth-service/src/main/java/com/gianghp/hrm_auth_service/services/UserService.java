package com.gianghp.hrm_auth_service.services;

import com.gianghp.hrm.dtos.EmployeeBasicDto;
import com.gianghp.hrm.enums.RoleType;
import com.gianghp.hrm.enums.UserStatus;
import com.gianghp.hrm_auth_service.dtos.*;
import com.gianghp.hrm_auth_service.entities.Role;
import com.gianghp.hrm_auth_service.entities.User;
import com.gianghp.hrm_auth_service.mappers.UserMapper;
import com.gianghp.hrm_auth_service.producers.KafkaUserProducer;
import com.gianghp.hrm_auth_service.repositories.RoleRepository;
import com.gianghp.hrm_auth_service.repositories.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional
public class UserService {
    
    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final PasswordEncoder passwordEncoder;
    private final UserMapper userMapper;
    private final KafkaUserProducer kafkaUserProducer;

    public Page<UserDto> getAllUsers(Pageable pageable) {
        return userRepository.findAll(pageable).map(userMapper::toDto);
    }

    public UserDto getUserById(UUID id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + id));
        return userMapper.toDto(user);
    }

    public UserDto createUser(CreateUserRequestDto request) {
        // Check if username already exists
        if (userRepository.existsByUsername(request.getUsername())) {
            throw new RuntimeException("Username already exists: " + request.getUsername());
        }

        // Check if email already exists
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new RuntimeException("Email already exists: " + request.getEmail());
        }

        // Find role
        Role role = roleRepository.findByName(RoleType.EMPLOYEE)
                .orElseThrow(() -> new RuntimeException("Role not found: " + RoleType.EMPLOYEE));

        // Create new user
        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setPasswordHash(passwordEncoder.encode(request.getPassword()));
        user.setRole(role);
        user.setStatus(UserStatus.ACTIVE);

        User savedUser = userRepository.save(user);

        EmployeeBasicDto employeeBasicDto = request.getEmployee();
        employeeBasicDto.setUserId(savedUser.getId());
        employeeBasicDto.setFullName(employeeBasicDto.getFirstName() + " " + employeeBasicDto.getLastName());
        employeeBasicDto.setBaseSalary(request.getBaseSalary());
        kafkaUserProducer.sendEmployeeWhenUserCreated(employeeBasicDto);

        return userMapper.toDto(savedUser);
    }

    public UserDto updateUser(UUID id, UpdateUserRequest request) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + id));

        // Update email if provided
        if (request.getEmail() != null && !request.getEmail().equals(user.getEmail())) {
            if (userRepository.existsByEmail(request.getEmail())) {
                throw new RuntimeException("Email already exists: " + request.getEmail());
            }
            user.setEmail(request.getEmail());
        }

        // Update role if provided
        if (request.getRole() != null) {
            Role role = roleRepository.findByName(request.getRole())
                    .orElseThrow(() -> new RuntimeException("Role not found: " + request.getRole()));
            user.setRole(role);
        }

        User updatedUser = userRepository.save(user);
        return userMapper.toDto(updatedUser);
    }

    public UserDto updateUserStatus(UUID id, UserStatusRequest request) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + id));

        user.setStatus(request.getStatus());
        User updatedUser = userRepository.save(user);
        return userMapper.toDto(updatedUser);
    }

    public void deleteUser(UUID id) {
        if (!userRepository.existsById(id)) {
            throw new RuntimeException("User not found with id: " + id);
        }
        userRepository.deleteById(id);
    }
}
