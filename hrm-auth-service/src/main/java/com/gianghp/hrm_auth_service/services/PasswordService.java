package com.gianghp.hrm_auth_service.services;

import com.gianghp.hrm_auth_service.dtos.ChangePasswordRequest;
import com.gianghp.hrm_auth_service.dtos.ForgotPasswordRequest;
import com.gianghp.hrm_auth_service.dtos.ResetPasswordRequest;
import com.gianghp.hrm_auth_service.entities.User;
import com.gianghp.hrm_auth_service.repositories.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class PasswordService {
    
    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    
    // In-memory storage for reset tokens (in production, use Redis or database)
    private final Map<String, String> resetTokens = new HashMap<>();
    private final SecureRandom secureRandom = new SecureRandom();

    public void changePassword(ChangePasswordRequest request) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("User not found: " + username));

        // Verify current password
        if (!passwordEncoder.matches(request.getCurrentPassword(), user.getPasswordHash())) {
            throw new RuntimeException("Current password is incorrect");
        }

        // Update password
        user.setPasswordHash(passwordEncoder.encode(request.getNewPassword()));
        userRepository.save(user);
        
        log.info("Password changed successfully for user: {}", username);
    }

    public String forgotPassword(ForgotPasswordRequest request) {
        User user = userRepository.findByEmail(request.getEmail())
                .orElseThrow(() -> new RuntimeException("User not found with email: " + request.getEmail()));

        // Generate reset token
        String token = generateResetToken();
        resetTokens.put(token, user.getEmail());
        
        log.info("Password reset token generated for user: {}", user.getUsername());
        
        // In production, send email with reset link
        // emailService.sendPasswordResetEmail(user.getEmail(), token);
        
        return token; // Return token for testing purposes
    }

    public void resetPassword(ResetPasswordRequest request) {
        String email = resetTokens.get(request.getToken());
        if (email == null) {
            throw new RuntimeException("Invalid or expired reset token");
        }

        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new RuntimeException("User not found with email: " + email));

        // Update password
        user.setPasswordHash(passwordEncoder.encode(request.getNewPassword()));
        userRepository.save(user);

        // Remove used token
        resetTokens.remove(request.getToken());
        
        log.info("Password reset successfully for user: {}", user.getUsername());
    }

    private String generateResetToken() {
        byte[] randomBytes = new byte[32];
        secureRandom.nextBytes(randomBytes);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(randomBytes);
    }
}
