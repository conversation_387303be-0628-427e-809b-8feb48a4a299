package com.gianghp.hrm_auth_service.services;

import com.gianghp.hrm.dtos.BaseSalaryBasicDto;
import com.gianghp.hrm.dtos.EmployeeBasicDto;
import com.gianghp.hrm.enums.CurrencyCode;
import com.gianghp.hrm.enums.Gender;
import com.gianghp.hrm.enums.RoleType;
import com.gianghp.hrm.enums.UserStatus;
import com.gianghp.hrm.enums.WorkLocationType;
import com.gianghp.hrm.enums.WorkType;
import com.gianghp.hrm_auth_service.dtos.CreateUserRequestDto;
import com.gianghp.hrm_auth_service.entities.Permission;
import com.gianghp.hrm_auth_service.entities.Role;
import com.gianghp.hrm_auth_service.entities.RolePermission;
import com.gianghp.hrm_auth_service.entities.User;
import com.gianghp.hrm_auth_service.repositories.PermissionRepository;
import com.gianghp.hrm_auth_service.repositories.RolePermissionRepository;
import com.gianghp.hrm_auth_service.repositories.RoleRepository;
import com.gianghp.hrm_auth_service.repositories.UserRepository;
import java.math.BigDecimal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class DataInitializer {

    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final PermissionRepository permissionRepository;
    private final RolePermissionRepository rolePermissionRepository;
    private final PasswordEncoder passwordEncoder;
    private final UserService userService;

    private void initRoles() {
        // Kiểm tra và tạo các role nếu chưa tồn tại
        for (RoleType roleType : RoleType.values()) {
            if (roleRepository.findByName(roleType).isEmpty()) {
                Role role = new Role();
                role.setName(roleType);
                role.setDescription(roleType.name() + " role");
                roleRepository.save(role);
                log.info("Created role: {}", roleType);
            }
        }
    }

    private void initDefaultUsers() {
        // Create default admin and manager users (keep existing)
        createUserIfNotExists("admin", "<EMAIL>", "admin123", RoleType.ADMIN);
        createUserIfNotExists("manager", "<EMAIL>", "manager123", RoleType.MANAGER);

        // Create sample employee users with Kafka integration
        if (userRepository.count() < 3) {
            createEmployeeUsersWithKafka();
        }

        log.info("=== Default Login Credentials ===");
        log.info("Admin: username=admin, password=admin123");
        log.info("Manager: username=manager, password=manager123");
        log.info("Sample Employees: emp001-emp005, password=employee123");
        log.info("================================");
    }

    private void createUserIfNotExists(String username, String email, String password, RoleType roleType) {
        if (userRepository.findByUsername(username).isEmpty()) {
            Optional<Role> role = roleRepository.findByName(roleType);

            if (role.isEmpty()) {
                log.error("{} role not found, cannot create {} user", roleType, username);
                return;
            }

            User user = new User();
            user.setUsername(username);
            user.setEmail(email);
            user.setPasswordHash(passwordEncoder.encode(password));
            user.setRole(role.get());
            user.setStatus(UserStatus.ACTIVE);

            userRepository.save(user);
            log.info("Created default {} user: {}", roleType, username);
        }
    }

    private void initPermissions() {
        if (permissionRepository.count() == 0) {
            log.info("Initializing default permissions...");

            // User management permissions
            createPermissionIfNotExists("USER_READ", "Read user information");
            createPermissionIfNotExists("USER_WRITE", "Create and update users");
            createPermissionIfNotExists("USER_DELETE", "Delete users");
            createPermissionIfNotExists("USER_MANAGE_ROLES", "Manage user roles");

            // Leave management permissions
            createPermissionIfNotExists("LEAVE_REQUEST_VIEW", "View leave requests");
            createPermissionIfNotExists("LEAVE_REQUEST_CREATE", "Create leave requests");
            createPermissionIfNotExists("LEAVE_REQUEST_APPROVE", "Approve leave requests");

            // Payroll permissions
            createPermissionIfNotExists("PAYROLL_VIEW", "View payroll information");
            createPermissionIfNotExists("PAYROLL_PROCESS", "Process payroll");

            // System permissions
            createPermissionIfNotExists("SYSTEM_ADMIN", "Full system administration");

            log.info("Default permissions created successfully");
        }
    }

    private void createPermissionIfNotExists(String name, String description) {
        if (permissionRepository.findByName(name).isEmpty()) {
            Permission permission = new Permission();
            permission.setName(name);
            permission.setDescription(description);
            permissionRepository.save(permission);
        }
    }

    private void initRolePermissions() {
        log.info("Initializing role permissions...");

        // ADMIN - Full access
        assignPermissionsToRole(RoleType.ADMIN,
            "SYSTEM_ADMIN", "USER_READ", "USER_WRITE", "USER_DELETE", "USER_MANAGE_ROLES",
            "LEAVE_REQUEST_VIEW", "LEAVE_REQUEST_CREATE", "LEAVE_REQUEST_APPROVE",
            "PAYROLL_VIEW", "PAYROLL_PROCESS");

        // MANAGER - Management permissions
        assignPermissionsToRole(RoleType.MANAGER,
            "USER_READ", "LEAVE_REQUEST_VIEW", "LEAVE_REQUEST_APPROVE",
            "PAYROLL_VIEW");

        // EMPLOYEE - Basic permissions
        assignPermissionsToRole(RoleType.EMPLOYEE,
            "LEAVE_REQUEST_VIEW", "LEAVE_REQUEST_CREATE");

        log.info("Role permissions assigned successfully");
    }

    private void createEmployeeUsersWithKafka() {
        log.info("Creating sample employee users with Kafka integration...");

        LocalDate today = LocalDate.now();
        CurrencyCode currency = CurrencyCode.VND;

        List<CreateUserRequestDto> employeeRequests = List.of(
            // --- Information Technology ---
            createEmployeeRequest("emp001", "<EMAIL>", "Alice", "Nguyen", "Information Technology", "Software Engineer",
                UUID.fromString("eb8b9c78-a4fa-4a4c-96bf-8f11e32cf969"), UUID.fromString("9a464142-0cce-460b-a7e1-77c270ac2057"),
                new BigDecimal("********"), currency, today),
            createEmployeeRequest("emp002", "<EMAIL>", "Bob", "Tran", "Information Technology", "Senior Software Engineer",
                UUID.fromString("eb8b9c78-a4fa-4a4c-96bf-8f11e32cf969"), UUID.fromString("7ed6f5f7-d427-40f2-872a-04fbc0382485"),
                new BigDecimal("********"), currency, today),
            createEmployeeRequest("emp003", "<EMAIL>", "Carol", "Pham", "Information Technology", "Tech Lead",
                UUID.fromString("eb8b9c78-a4fa-4a4c-96bf-8f11e32cf969"), UUID.fromString("1cd9cd56-2147-48d7-b50f-911c0cd9b0a5"),
                new BigDecimal("18000000"), currency, today),
            createEmployeeRequest("emp004", "<EMAIL>", "David", "Le", "Information Technology", "DevOps Engineer",
                UUID.fromString("eb8b9c78-a4fa-4a4c-96bf-8f11e32cf969"), UUID.fromString("0f577c71-b0c8-4445-a537-85deaa23a09d"),
                new BigDecimal("14000000"), currency, today),
            createEmployeeRequest("emp005", "<EMAIL>", "Eva", "Ho", "Information Technology", "QA Engineer",
                UUID.fromString("eb8b9c78-a4fa-4a4c-96bf-8f11e32cf969"), UUID.fromString("6690ff7c-fe21-4f80-af4b-af3592aaa828"),
                new BigDecimal("13000000"), currency, today),

            // --- Human Resources ---
            createEmployeeRequest("emp006", "<EMAIL>", "Fiona", "Dang", "Human Resources", "HR Manager",
                UUID.fromString("8a90a225-a821-41b0-aa04-1837063d64e7"), UUID.fromString("641e33a7-2ef6-41a7-bfe8-b06fd0caebc8"),
                new BigDecimal("********"), currency, today),
            createEmployeeRequest("emp007", "<EMAIL>", "George", "Vo", "Human Resources", "HR Specialist",
                UUID.fromString("8a90a225-a821-41b0-aa04-1837063d64e7"), UUID.fromString("373fa48c-b55a-4cf4-9fc0-0ec3747f4282"),
                new BigDecimal("********"), currency, today),
            createEmployeeRequest("emp008", "<EMAIL>", "Helen", "Nguyen", "Human Resources", "Recruiter",
                UUID.fromString("8a90a225-a821-41b0-aa04-1837063d64e7"), UUID.fromString("77b9164f-99ba-44e8-8024-9aa430a9bd92"),
                new BigDecimal("11000000"), currency, today),
            createEmployeeRequest("emp009", "<EMAIL>", "Ian", "Pham", "Human Resources", "Operations Manager",
                UUID.fromString("8a90a225-a821-41b0-aa04-1837063d64e7"), UUID.fromString("********-d787-4811-820a-4434156be8dd"),
                new BigDecimal("********"), currency, today),
            createEmployeeRequest("emp010", "<EMAIL>", "Julia", "Le", "Human Resources", "Project Manager",
                UUID.fromString("8a90a225-a821-41b0-aa04-1837063d64e7"), UUID.fromString("2eb1af18-221e-463b-98a5-3484d10f739c"),
                new BigDecimal("********"), currency, today),

            // --- Finance & Accounting ---
            createEmployeeRequest("emp011", "<EMAIL>", "Kevin", "Nguyen", "Finance & Accounting", "Finance Manager",
                UUID.fromString("1c5cdb04-53de-4486-9c72-55e0d6c094ec"), UUID.fromString("2de40eac-33dc-472f-bf29-5e88e9cfb712"),
                new BigDecimal("********"), currency, today),
            createEmployeeRequest("emp012", "<EMAIL>", "Lisa", "Hoang", "Finance & Accounting", "Accountant",
                UUID.fromString("1c5cdb04-53de-4486-9c72-55e0d6c094ec"), UUID.fromString("1235c39a-d104-43a0-9e33-cd83d9a9aefb"),
                new BigDecimal("********"), currency, today),
            createEmployeeRequest("emp013", "<EMAIL>", "Michael", "Vo", "Finance & Accounting", "Financial Analyst",
                UUID.fromString("1c5cdb04-53de-4486-9c72-55e0d6c094ec"), UUID.fromString("944c42ca-c6c4-4cb0-84ac-b99501407cd3"),
                new BigDecimal("********"), currency, today),
            createEmployeeRequest("emp014", "<EMAIL>", "Nina", "Le", "Finance & Accounting", "Project Manager",
                UUID.fromString("1c5cdb04-53de-4486-9c72-55e0d6c094ec"), UUID.fromString("2eb1af18-221e-463b-98a5-3484d10f739c"),
                new BigDecimal("********"), currency, today),
            createEmployeeRequest("emp015", "<EMAIL>", "Oliver", "Tran", "Finance & Accounting", "Operations Manager",
                UUID.fromString("1c5cdb04-53de-4486-9c72-55e0d6c094ec"), UUID.fromString("********-d787-4811-820a-4434156be8dd"),
                new BigDecimal("********"), currency, today),
            // --- Marketing & Sales ---
            createEmployeeRequest("emp016", "<EMAIL>", "Paul", "Pham", "Marketing & Sales", "Marketing Manager",
                UUID.fromString("102f4cb9-59fb-4326-ad1c-577302cbce72"), UUID.fromString("be127687-b407-48b1-836f-601721ce2dcf"),
                new BigDecimal("********"), currency, today),
            createEmployeeRequest("emp017", "<EMAIL>", "Quinn", "Nguyen", "Marketing & Sales", "Sales Representative",
                UUID.fromString("102f4cb9-59fb-4326-ad1c-577302cbce72"), UUID.fromString("b60662a8-31d0-46eb-b4e9-492fa111e449"),
                new BigDecimal("********"), currency, today),
            createEmployeeRequest("emp018", "<EMAIL>", "Rachel", "Vo", "Marketing & Sales", "Digital Marketing Specialist",
                UUID.fromString("102f4cb9-59fb-4326-ad1c-577302cbce72"), UUID.fromString("8805f1e1-2291-421b-83f2-2cd73698c948"),
                new BigDecimal("13000000"), currency, today),
            createEmployeeRequest("emp019", "<EMAIL>", "Sam", "Le", "Marketing & Sales", "Team Lead",
                UUID.fromString("102f4cb9-59fb-4326-ad1c-577302cbce72"), UUID.fromString("0f75836b-0e13-47ec-85cf-3fc591e2116c"),
                new BigDecimal("14000000"), currency, today),
            createEmployeeRequest("emp020", "<EMAIL>", "Tina", "Ho", "Marketing & Sales", "Department Head",
                UUID.fromString("102f4cb9-59fb-4326-ad1c-577302cbce72"), UUID.fromString("7352be2c-b5dc-4e20-8760-a5d7770228bb"),
                new BigDecimal("18000000"), currency, today),

            // --- Operations ---
            createEmployeeRequest("emp021", "<EMAIL>", "Ursula", "Nguyen", "Operations", "Operations Manager",
                UUID.fromString("e4fff129-734c-4bbc-99df-20e8f8fa2383"), UUID.fromString("********-d787-4811-820a-4434156be8dd"),
                new BigDecimal("********"), currency, today),
            createEmployeeRequest("emp022", "<EMAIL>", "Victor", "Pham", "Operations", "Project Manager",
                UUID.fromString("e4fff129-734c-4bbc-99df-20e8f8fa2383"), UUID.fromString("2eb1af18-221e-463b-98a5-3484d10f739c"),
                new BigDecimal("********"), currency, today),
            createEmployeeRequest("emp023", "<EMAIL>", "Wendy", "Vo", "Operations", "HR Manager",
                UUID.fromString("e4fff129-734c-4bbc-99df-20e8f8fa2383"), UUID.fromString("641e33a7-2ef6-41a7-bfe8-b06fd0caebc8"),
                new BigDecimal("********"), currency, today),
            createEmployeeRequest("emp024", "<EMAIL>", "Xander", "Le", "Operations", "CEO",
                UUID.fromString("e4fff129-734c-4bbc-99df-20e8f8fa2383"), UUID.fromString("8677881d-d87e-4c00-a2d4-75367ef0f543"),
                new BigDecimal("30000000"), currency, today),
            createEmployeeRequest("emp025", "<EMAIL>", "Yuna", "Tran", "Operations", "CTO",
                UUID.fromString("e4fff129-734c-4bbc-99df-20e8f8fa2383"), UUID.fromString("fed64558-1e76-4115-a0bb-e697d60a75fa"),
                new BigDecimal("28000000"), currency, today),

            // --- Quality Assurance ---
            createEmployeeRequest("emp026", "<EMAIL>", "Zane", "Nguyen", "Quality Assurance", "QA Engineer",
                UUID.fromString("351f1a7d-7a67-4006-b814-ec14f24ab64c"), UUID.fromString("6690ff7c-fe21-4f80-af4b-af3592aaa828"),
                new BigDecimal("13000000"), currency, today),
            createEmployeeRequest("emp027", "<EMAIL>", "Amy", "Pham", "Quality Assurance", "Project Manager",
                UUID.fromString("351f1a7d-7a67-4006-b814-ec14f24ab64c"), UUID.fromString("2eb1af18-221e-463b-98a5-3484d10f739c"),
                new BigDecimal("********"), currency, today),
            createEmployeeRequest("emp028", "<EMAIL>", "Brian", "Tran", "Quality Assurance", "Team Lead",
                UUID.fromString("351f1a7d-7a67-4006-b814-ec14f24ab64c"), UUID.fromString("0f75836b-0e13-47ec-85cf-3fc591e2116c"),
                new BigDecimal("14500000"), currency, today),
            createEmployeeRequest("emp029", "<EMAIL>", "Clara", "Ho", "Quality Assurance", "HR Specialist",
                UUID.fromString("351f1a7d-7a67-4006-b814-ec14f24ab64c"), UUID.fromString("373fa48c-b55a-4cf4-9fc0-0ec3747f4282"),
                new BigDecimal("********"), currency, today),
            createEmployeeRequest("emp030", "<EMAIL>", "Dan", "Le", "Quality Assurance", "Software Engineer",
                UUID.fromString("351f1a7d-7a67-4006-b814-ec14f24ab64c"), UUID.fromString("9a464142-0cce-460b-a7e1-77c270ac2057"),
                new BigDecimal("********"), currency, today)
        );





        for (CreateUserRequestDto request : employeeRequests) {
            try {
                if (userRepository.findByUsername(request.getUsername()).isEmpty()) {
                    userService.createUser(request); // This will trigger Kafka producer
                    log.info("Created employee user: {} with Kafka event", request.getUsername());
                } else {
                    log.info("Employee user {} already exists, skipping", request.getUsername());
                }
            } catch (Exception e) {
                log.error("Failed to create employee user {}: {}", request.getUsername(), e.getMessage());
            }
        }
    }

    private CreateUserRequestDto createEmployeeRequest(String username, String email, String firstName, String lastName, String department, String designation, UUID departmentId, UUID designationId, BigDecimal baseSalary, CurrencyCode currency, LocalDate effectiveDate) {
        CreateUserRequestDto request = new CreateUserRequestDto();
        request.setUsername(username);
        request.setEmail(email);
        request.setPassword("employee123");

        int empNum = Integer.parseInt(username.substring(3, 6));

        EmployeeBasicDto employee = EmployeeBasicDto.builder()
                .employeeCode(username.toUpperCase())
            .firstName(firstName)
            .lastName(lastName)
            .email(email)
            .mobileNumber("+1234567" + String.format("%03d", empNum))
            .dateOfBirth(LocalDate.of(1990 + (empNum % 10),
                                    Math.max(1, (empNum % 12) + 1),
                                    Math.max(1, (empNum % 28) + 1)))
            .gender(empNum % 2 == 0 ? Gender.MALE : Gender.FEMALE)
            .nationality("American")
            .address("123 " + department + " Street, City")
            .city("New York")
            .state("NY")
            .dateOfJoining(LocalDate.now())
            .workLocationType(WorkLocationType.ON_SITE)
            .type(WorkType.FULL_TIME)
            .departmentName(department)
            .designationName(designation)
                .departmentId(departmentId)
                .designationId(designationId)
            .build();
        employee.setBaseSalary(BaseSalaryBasicDto.builder()
                .amount(baseSalary)
                .currency(currency)
                .effectiveDate(effectiveDate)
                .build());

        request.setEmployee(employee);
        return request;
    }

    private void assignPermissionsToRole(RoleType roleType, String... permissionNames) {
        Role role = roleRepository.findByName(roleType).orElse(null);
        if (role == null) {
            log.warn("Role {} not found, skipping permission assignment", roleType);
            return;
        }

        for (String permissionName : permissionNames) {
            Permission permission = permissionRepository.findByName(permissionName).orElse(null);
            if (permission == null) {
                log.warn("Permission {} not found, skipping", permissionName);
                continue;
            }

            // Check if role-permission relationship already exists
            if (rolePermissionRepository.findByRoleAndPermission(role, permission).isEmpty()) {
                RolePermission rolePermission = new RolePermission();
                rolePermission.setRole(role);
                rolePermission.setPermission(permission);
                rolePermissionRepository.save(rolePermission);
            }
        }
    }
}