// src/main/java/com/yourcompany/authservice/service/UserService.java
package com.gianghp.hrm_auth_service.services;

import com.gianghp.hrm.enums.RoleType;
import com.gianghp.hrm_auth_service.security.JwtService;
import com.gianghp.hrm_auth_service.dtos.LoginRequest;
import com.gianghp.hrm_auth_service.dtos.UserDto;
import com.gianghp.hrm_auth_service.entities.User;
import com.gianghp.hrm_auth_service.repositories.UserRepository;
import lombok.AllArgsConstructor; // Để Lombok tạo constructor cho dependency injection
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@AllArgsConstructor
@Slf4j
public class AuthService {

    private final AuthenticationManager authenticationManager;
    private final JwtService jwtService;
    private final UserRepository userRepository;

    public Map<String, Object> verify(LoginRequest loginRequest) {
        Map<String, Object> result = new HashMap<>();
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                        loginRequest.getUsername(),
                        loginRequest.getPassword()
                )
        );

        log.info("Authentication for user: {}, is Authenticated: {}", loginRequest.getUsername(), authentication.isAuthenticated());

        if (authentication.isAuthenticated()) {
            // Lấy thông tin user từ Authentication object

            String role = authentication.getAuthorities().stream()
                    .findFirst() // Nếu bạn chỉ có một role
                    .map(grantedAuthority -> {
                        String authority = grantedAuthority.getAuthority(); // VD: "ROLE_ADMIN"
                        return authority.startsWith("ROLE_") ? authority.substring(5) : authority;
                    })
                    .orElse("USER");

            // ✅ Lấy thông tin người dùng từ database

            User account = userRepository.findByUsername(loginRequest.getUsername())
                    .orElseThrow(() -> new UsernameNotFoundException("User not found"));

            UserDto user = new UserDto();
            user.setId(account.getId());
            user.setUsername(account.getUsername());
            user.setRole(RoleType.valueOf(role));
            user.setEmail(account.getEmail());
            user.setStatus(account.getStatus());

            // ✅ Trả response
            result.put("token", jwtService.generateToken(account.getId(), account.getUsername(), role));
            result.put("user", user);

            return result;
        } else {
            return Map.of("error", "Invalid username or password");
        }
    }

}