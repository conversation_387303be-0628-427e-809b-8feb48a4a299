package com.gianghp.hrm_auth_service.mappers;

import com.gianghp.hrm_auth_service.dtos.UserDto;
import com.gianghp.hrm_auth_service.entities.User;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface UserMapper {

    UserMapper INSTANCE = Mappers.getMapper(UserMapper.class);

    @Mapping(source = "role.name", target = "role")
    UserDto toDto(User user);
}
