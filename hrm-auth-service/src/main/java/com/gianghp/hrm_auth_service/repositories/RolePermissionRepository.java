package com.gianghp.hrm_auth_service.repositories;

import com.gianghp.hrm_auth_service.entities.Permission;
import com.gianghp.hrm_auth_service.entities.Role;
import com.gianghp.hrm_auth_service.entities.RolePermission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface RolePermissionRepository extends JpaRepository<RolePermission, UUID> {
    List<RolePermission> findByRoleId(UUID roleId);
    List<RolePermission> findByPermissionId(UUID permissionId);
    Optional<RolePermission> findByRoleAndPermission(Role role, Permission permission);
    void deleteByRoleIdAndPermissionId(UUID roleId, UUID permissionId);
}