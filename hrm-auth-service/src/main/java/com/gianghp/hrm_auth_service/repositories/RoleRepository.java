package com.gianghp.hrm_auth_service.repositories;

import com.gianghp.hrm.enums.RoleType;
import com.gianghp.hrm_auth_service.entities.Role;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface RoleRepository extends JpaRepository<Role, UUID> {
    Optional<Role> findByName(RoleType name);
    boolean existsByName(RoleType name);
}