package com.gianghp.hrm_auth_service.models;

import com.gianghp.hrm.enums.UserStatus;
import com.gianghp.hrm_auth_service.entities.RolePermission;
import com.gianghp.hrm_auth_service.entities.User;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Getter
@AllArgsConstructor
public class UserPrincipal implements UserDetails {
    // Utility method để lấy thông tin user
    private User user;

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        List<GrantedAuthority> authorities = new ArrayList<>();

        // Thêm role authority
        if (user.getRole() != null) {
            // Thêm role với prefix ROLE_
            authorities.add(new SimpleGrantedAuthority("ROLE_" + user.getRole().getName().name()));

            // Thêm permissions từ role
            if (user.getRole().getRolePermissions() != null) {
                user.getRole().getRolePermissions().forEach(rolePermission -> {
                    if (rolePermission.getPermission() != null) {
                        authorities.add(new SimpleGrantedAuthority(rolePermission.getPermission().getName()));
                    }
                });
            }
        }

        return authorities;
    }

    @Override
    public String getPassword() {
        return user.getPasswordHash();
    }

    @Override
    public String getUsername() {
        return user.getUsername(); // Sử dụng username cho Spring Security
    }

    @Override
    public boolean isAccountNonExpired() {
        // Account không bao giờ hết hạn trong hệ thống này
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        // Kiểm tra user status, chỉ ACTIVE và PENDING là không bị khóa
        return user.getStatus() == UserStatus.ACTIVE || user.getStatus() == UserStatus.ON_LEAVE;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        // Credentials không bao giờ hết hạn trong hệ thống này
        return true;
    }

    @Override
    public boolean isEnabled() {
        // Chỉ user có status ACTIVE mới được enable
        return user.getStatus() == UserStatus.ACTIVE;
    }

    // Utility method để kiểm tra có role cụ thể không
    public boolean hasRole(String roleName) {
        return getAuthorities().stream()
                .anyMatch(auth -> auth.getAuthority().equals("ROLE_" + roleName));
    }

    // Utility method để kiểm tra có permission cụ thể không
    public boolean hasPermission(String permissionName) {
        return getAuthorities().stream()
                .anyMatch(auth -> auth.getAuthority().equals(permissionName));
    }
}
