package com.gianghp.hrm_auth_service.configs;

import com.gianghp.hrm_auth_service.configs.AuthSecurityConfig;
import com.gianghp.hrm_auth_service.security.JwtFilter;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SecurityConfig extends AuthSecurityConfig {

    private final JwtFilter jwtFilter;

    public SecurityConfig(JwtFilter jwtFilter) {
        this.jwtFilter = jwtFilter;
    }

    @Override
    protected String[] publicRoutes() {
        return new String[]{"/auth/login"};
    }

    @Override
    protected JwtFilter jwtFilter() {
        return jwtFilter;
    }
}
