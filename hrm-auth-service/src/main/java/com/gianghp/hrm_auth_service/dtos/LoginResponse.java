package com.gianghp.hrm_auth_service.dtos;

import com.gianghp.hrm.enums.RoleType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO cho phản hồi đăng nhập - có thể dùng chung cho tất cả các service
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LoginResponse {
    private String accessToken;
    private String tokenType = "Bearer";
    private UserDto user;
    
    public LoginResponse(String accessToken, UserDto user) {
        this.accessToken = accessToken;
        this.tokenType = "Bearer";
        this.user = user;
    }
}
