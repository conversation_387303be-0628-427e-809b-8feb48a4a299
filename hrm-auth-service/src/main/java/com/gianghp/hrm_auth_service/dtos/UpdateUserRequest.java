package com.gianghp.hrm_auth_service.dtos;

import com.gianghp.hrm.enums.RoleType;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateUserRequest {
    @Email(message = "Email should be valid")
    private String email;

    private RoleType role;
}
