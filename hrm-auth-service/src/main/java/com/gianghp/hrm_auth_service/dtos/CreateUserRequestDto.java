package com.gianghp.hrm_auth_service.dtos;

import com.gianghp.hrm.dtos.BaseSalaryBasicDto;
import com.gianghp.hrm.dtos.EmployeeBasicDto;
import com.gianghp.hrm.enums.RoleType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateUserRequestDto {
    private String username;
    private String email;
    private String password;
    private EmployeeBasicDto employee;
    private BaseSalaryBasicDto baseSalary;
}
