package com.gianghp.hrm_auth_service.consumers;

import com.gianghp.hrm.constants.TopicConstants;
import com.gianghp.hrm.dtos.EmployeeBasicDto;
import com.gianghp.hrm.dtos.EmployeeCreateFailedDto;
import com.gianghp.hrm_auth_service.acl.UserACL;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
@Slf4j
@RequiredArgsConstructor
public class KafkaUserConsumer {
    private final UserACL userACL;

    @KafkaListener(topics = TopicConstants.EMPLOYEE_CREATE_FAILED)
    public void consumeEmployeeCreatedFailed(EmployeeCreateFailedDto message) {
        log.info("Received from {}: {}", TopicConstants.EMPLOYEE_CREATE_FAILED, message);
        userACL.handleEmployeeCreatedEventFailed(message.getUserId());
    }
}
