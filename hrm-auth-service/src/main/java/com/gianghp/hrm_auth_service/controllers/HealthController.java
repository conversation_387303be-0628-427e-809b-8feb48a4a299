package com.gianghp.hrm_auth_service.controllers;

import com.gianghp.hrm.dtos.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/health")
@RequiredArgsConstructor
@Slf4j
public class HealthController {

  @GetMapping
  public ResponseEntity<ApiResponse> health() {
    log.info(">>> AUTH SERVICE GOT FORWARD-AUTH REQUEST <<<");
    HttpHeaders headers = new HttpHeaders();
    headers.add("X-Auth-Service-Status", "OK");
    headers.add("X-Checked-By", "auth-service");

    return ResponseEntity
        .ok()
        .headers(headers)
        .body(ApiResponse.success("OK"));
  }
}
