spring.application.name=hrm-auth-service
spring.config.import=classpath:application-common.properties
server.port=6010

# Database auth
spring.datasource.url=****************************************
spring.datasource.username=postgres
spring.datasource.password=ggmacket123
spring.jpa.hibernate.ddl-auto=update

# JWT Configuration - Auth service specific
jwt.expiration=864000000
jwt.secret=mySecretKey123456789012345678901234567890

# Kafka Configuration
spring.kafka.consumer.group-id=${spring.application.name}-group