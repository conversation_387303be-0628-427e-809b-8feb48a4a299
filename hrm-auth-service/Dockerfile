# Stage 1: Build
FROM eclipse-temurin:21-jdk AS build

WORKDIR /build

COPY gradlew gradlew.bat ./
COPY gradle/ gradle/
COPY settings.gradle build.gradle ./

COPY hrm-common/ hrm-common/
COPY src/ src/

RUN chmod +x gradlew
RUN ./gradlew build -x test --no-daemon

# Stage 2: Run
FROM eclipse-temurin:21-jre-jammy

WORKDIR /app

RUN groupadd -r hrm && useradd -r -g hrm hrm
COPY --from=build /build/build/libs/*.jar app.jar
RUN chown hrm:hrm app..\gradlew.bat build --no-daemon

USER hrm
EXPOSE 6010

ENTRYPOINT ["java", "-jar", "app.jar"]
